# Токены для Tinkoff Invest API для разных аккаунтов
# В CONFIG.json указывается id аккаунта и соответствующий токен
T_INVEST_TOKEN_1=
T_INVEST_TOKEN_2=
# Добавьте больше токенов по необходимости: T_INVEST_TOKEN_3, T_INVEST_TOKEN_4, etc.

# ACCOUNT_ID теперь указывается в CONFIG.json для каждого аккаунта
# ACCOUNT_ID может быть:
# - точным id счета (строка вида d:12345-... из API)
# - BROKER — выбрать брокерский счет
# - ISS — выбрать ИИС
# - INDEX:N — выбрать счет по индексу (смотрите bun run accounts)
# Чтобы вывести список счетов с их id: bun run accounts

OPENROUTER_API_KEY = ''
OPENROUTER_MODEL = 'qwen/qwen3-235b-a22b-2507'