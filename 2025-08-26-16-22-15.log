$ DEBUG=bot:main,bot:provider,bot:balancer bun run ./src/index.ts
2025-08-26T13:22:15.683Z bot:main main start
2025-08-26T13:22:15.684Z bot:provider Getting accounts list
2025-08-26T13:22:16.606Z bot:provider accountsResponse {
  accounts: [
    {
      id: '**********',
      type: 1,
      name: 'Брокерский счет 4',
      status: 2,
      openedDate: 2025-08-08T00:00:00.000Z,
      closedDate: 1970-01-01T00:00:00.000Z,
      accessLevel: 1
    }
  ]
}
2025-08-26T13:22:16.609Z bot:provider Selected account by index {
  id: '**********',
  type: 1,
  name: 'Брокерский счет 4',
  status: 2,
  openedDate: 2025-08-08T00:00:00.000Z,
  closedDate: 1970-01-01T00:00:00.000Z,
  accessLevel: 1
}
2025-08-26T13:22:16.610Z bot:provider Getting shares list
2025-08-26T13:22:27.406Z bot:provider shares count 1941
2025-08-26T13:22:32.413Z bot:provider Getting ETFs list
2025-08-26T13:22:33.582Z bot:provider etfs count 276
2025-08-26T13:22:38.585Z bot:provider Getting bonds list
2025-08-26T13:22:47.233Z bot:provider bonds count 1392
2025-08-26T13:22:52.236Z bot:provider Getting currencies list
2025-08-26T13:22:52.496Z bot:provider currencies count 16
2025-08-26T13:22:57.498Z bot:provider Getting futures list
2025-08-26T13:22:59.502Z bot:provider futures count 342
2025-08-26T13:23:04.506Z bot:provider =========================
2025-08-26T13:23:04.510Z bot:provider Checking trading schedule for MOEX. Current time: 2025-08-26T13:23:04.510Z
2025-08-26T13:23:04.510Z bot:provider Request params: from=2025-08-26T13:23:04.510Z, to=2025-08-27T13:23:04.510Z
2025-08-26T13:23:04.737Z bot:provider Trading schedules response: {
  "exchanges": [
    {
      "exchange": "MOEX",
      "days": [
        {
          "date": "2025-08-26T00:00:00.000Z",
          "isTradingDay": true,
          "startTime": "2025-08-26T07:00:00.000Z",
          "endTime": "2025-08-26T15:39:59.000Z",
          "openingAuctionStartTime": "2025-08-26T06:50:00.000Z",
          "closingAuctionEndTime": "2025-08-26T15:50:00.000Z",
          "closingAuctionStartTime": "2025-08-26T15:40:01.000Z",
          "openingAuctionEndTime": "2025-08-26T06:59:59.000Z",
          "intervals": [
            {
              "type": "regular_trading_session",
              "interval": {
                "startTs": "2025-08-26T07:00:00.000Z",
                "endTs": "2025-08-26T15:39:59.000Z"
              }
            },
            {
              "type": "regular_trading_session_main",
              "interval": {
                "startTs": "2025-08-26T07:00:00.000Z",
                "endTs": "2025-08-26T15:39:59.000Z"
              }
            },
            {
              "type": "opening_auction",
              "interval": {
                "startTs": "2025-08-26T06:50:00.000Z",
                "endTs": "2025-08-26T06:59:59.000Z"
              }
            },
            {
              "type": "opening_auction_main",
              "interval": {
                "startTs": "2025-08-26T06:50:00.000Z",
                "endTs": "2025-08-26T06:59:59.000Z"
              }
            },
            {
              "type": "closing_auction",
              "interval": {
                "startTs": "2025-08-26T15:40:01.000Z",
                "endTs": "2025-08-26T15:50:00.000Z"
              }
            },
            {
              "type": "closing_auction_main",
              "interval": {
                "startTs": "2025-08-26T15:40:01.000Z",
                "endTs": "2025-08-26T15:50:00.000Z"
              }
            }
          ]
        },
        {
          "date": "2025-08-27T00:00:00.000Z",
          "isTradingDay": true,
          "startTime": "2025-08-27T07:00:00.000Z",
          "endTime": "2025-08-27T15:39:59.000Z",
          "openingAuctionStartTime": "2025-08-27T06:50:00.000Z",
          "closingAuctionEndTime": "2025-08-27T15:50:00.000Z",
          "closingAuctionStartTime": "2025-08-27T15:40:01.000Z",
          "openingAuctionEndTime": "2025-08-27T06:59:59.000Z",
          "intervals": [
            {
              "type": "regular_trading_session",
              "interval": {
                "startTs": "2025-08-27T07:00:00.000Z",
                "endTs": "2025-08-27T15:39:59.000Z"
              }
            },
            {
              "type": "regular_trading_session_main",
              "interval": {
                "startTs": "2025-08-27T07:00:00.000Z",
                "endTs": "2025-08-27T15:39:59.000Z"
              }
            },
            {
              "type": "opening_auction",
              "interval": {
                "startTs": "2025-08-27T06:50:00.000Z",
                "endTs": "2025-08-27T06:59:59.000Z"
              }
            },
            {
              "type": "opening_auction_main",
              "interval": {
                "startTs": "2025-08-27T06:50:00.000Z",
                "endTs": "2025-08-27T06:59:59.000Z"
              }
            },
            {
              "type": "closing_auction",
              "interval": {
                "startTs": "2025-08-27T15:40:01.000Z",
                "endTs": "2025-08-27T15:50:00.000Z"
              }
            },
            {
              "type": "closing_auction_main",
              "interval": {
                "startTs": "2025-08-27T15:40:01.000Z",
                "endTs": "2025-08-27T15:50:00.000Z"
              }
            }
          ]
        }
      ]
    }
  ]
}
2025-08-26T13:23:04.737Z bot:provider Found 2 trading days in schedule
2025-08-26T13:23:04.737Z bot:provider Processing day: {
  "date": "2025-08-26T00:00:00.000Z",
  "isTradingDay": true,
  "startTime": "2025-08-26T07:00:00.000Z",
  "endTime": "2025-08-26T15:39:59.000Z",
  "openingAuctionStartTime": "2025-08-26T06:50:00.000Z",
  "closingAuctionEndTime": "2025-08-26T15:50:00.000Z",
  "closingAuctionStartTime": "2025-08-26T15:40:01.000Z",
  "openingAuctionEndTime": "2025-08-26T06:59:59.000Z",
  "intervals": [
    {
      "type": "regular_trading_session",
      "interval": {
        "startTs": "2025-08-26T07:00:00.000Z",
        "endTs": "2025-08-26T15:39:59.000Z"
      }
    },
    {
      "type": "regular_trading_session_main",
      "interval": {
        "startTs": "2025-08-26T07:00:00.000Z",
        "endTs": "2025-08-26T15:39:59.000Z"
      }
    },
    {
      "type": "opening_auction",
      "interval": {
        "startTs": "2025-08-26T06:50:00.000Z",
        "endTs": "2025-08-26T06:59:59.000Z"
      }
    },
    {
      "type": "opening_auction_main",
      "interval": {
        "startTs": "2025-08-26T06:50:00.000Z",
        "endTs": "2025-08-26T06:59:59.000Z"
      }
    },
    {
      "type": "closing_auction",
      "interval": {
        "startTs": "2025-08-26T15:40:01.000Z",
        "endTs": "2025-08-26T15:50:00.000Z"
      }
    },
    {
      "type": "closing_auction_main",
      "interval": {
        "startTs": "2025-08-26T15:40:01.000Z",
        "endTs": "2025-08-26T15:50:00.000Z"
      }
    }
  ]
}
2025-08-26T13:23:04.737Z bot:provider Session times: start=2025-08-26T07:00:00.000Z, end=2025-08-26T15:39:59.000Z
2025-08-26T13:23:04.737Z bot:provider Evening session: start=undefined, end=undefined
2025-08-26T13:23:04.737Z bot:provider Current time is within main trading session
2025-08-26T13:23:04.737Z bot:provider Getting portfolio
2025-08-26T13:23:05.032Z bot:provider portfolio {
  totalAmountShares: { currency: 'rub', units: 0, nano: 0 },
  totalAmountBonds: { currency: 'rub', units: 0, nano: 0 },
  totalAmountEtf: { currency: 'rub', units: 750, nano: 510000000 },
  totalAmountCurrencies: { currency: 'rub', units: -367, nano: -********* },
  totalAmountFutures: { currency: 'rub', units: 0, nano: 0 },
  expectedYield: { units: 0, nano: -********* },
  positions: [
    {
      figi: 'RUB000UTSTOM',
      instrumentType: 'currency',
      quantity: [Object],
      averagePositionPrice: [Object],
      expectedYield: [Object],
      currentNkd: undefined,
      averagePositionPricePt: [Object],
      currentPrice: [Object],
      averagePositionPriceFifo: [Object],
      quantityLots: [Object],
      blocked: false,
      blockedLots: [Object],
      positionUid: '33e24a92-aab0-409c-88b8-f2d57415b920',
      instrumentUid: 'a92e2e25-a698-45cc-a781-167cf465257c',
      varMargin: [Object],
      expectedYieldFifo: [Object],
      dailyYield: [Object]
    },
    {
      figi: 'TCS10A107563',
      instrumentType: 'etf',
      quantity: [Object],
      averagePositionPrice: [Object],
      expectedYield: [Object],
      currentNkd: undefined,
      averagePositionPricePt: [Object],
      currentPrice: [Object],
      averagePositionPriceFifo: [Object],
      quantityLots: [Object],
      blocked: false,
      blockedLots: [Object],
      positionUid: '8f747dc6-21bf-4f3a-878e-834880728a29',
      instrumentUid: 'd5cba263-cda7-440c-a21d-134fb5d334f6',
      varMargin: [Object],
      expectedYieldFifo: [Object],
      dailyYield: [Object]
    },
    {
      figi: 'TCS80A101X50',
      instrumentType: 'etf',
      quantity: [Object],
      averagePositionPrice: [Object],
      expectedYield: [Object],
      currentNkd: undefined,
      averagePositionPricePt: [Object],
      currentPrice: [Object],
      averagePositionPriceFifo: [Object],
      quantityLots: [Object],
      blocked: false,
      blockedLots: [Object],
      positionUid: '46dd8924-24e9-4581-afd2-97e0ca89009c',
      instrumentUid: 'de82be66-3b9b-4612-9572-61e3c6039013',
      varMargin: [Object],
      expectedYieldFifo: [Object],
      dailyYield: [Object]
    },
    {
      figi: 'BBG000000001',
      instrumentType: 'etf',
      quantity: [Object],
      averagePositionPrice: [Object],
      expectedYield: [Object],
      currentNkd: undefined,
      averagePositionPricePt: [Object],
      currentPrice: [Object],
      averagePositionPriceFifo: [Object],
      quantityLots: [Object],
      blocked: false,
      blockedLots: [Object],
      positionUid: '8005e2ec-66b3-49ae-9711-a424d9c9b61b',
      instrumentUid: 'e2d0dbac-d354-4c36-a5ed-e5aae42ffc76',
      varMargin: [Object],
      expectedYieldFifo: [Object],
      dailyYield: [Object]
    },
    {
      figi: 'TCS60A101X76',
      instrumentType: 'etf',
      quantity: [Object],
      averagePositionPrice: [Object],
      expectedYield: [Object],
      currentNkd: undefined,
      averagePositionPricePt: [Object],
      currentPrice: [Object],
      averagePositionPriceFifo: [Object],
      quantityLots: [Object],
      blocked: false,
      blockedLots: [Object],
      positionUid: 'ffd48f38-aa9b-491c-8e40-140a3f0bc931',
      instrumentUid: 'f509af83-6e71-462f-901f-bcb073f6773b',
      varMargin: [Object],
      expectedYieldFifo: [Object],
      dailyYield: [Object]
    },
    {
      figi: 'TCS00A10B0G9',
      instrumentType: 'etf',
      quantity: [Object],
      averagePositionPrice: [Object],
      expectedYield: [Object],
      currentNkd: undefined,
      averagePositionPricePt: [Object],
      currentPrice: [Object],
      averagePositionPriceFifo: [Object],
      quantityLots: [Object],
      blocked: false,
      blockedLots: [Object],
      positionUid: '0599cd56-02b0-4ceb-8045-eae66523979e',
      instrumentUid: 'fa20b747-e414-4922-963a-d19b752535b2',
      varMargin: [Object],
      expectedYieldFifo: [Object],
      dailyYield: [Object]
    }
  ],
  accountId: '**********',
  totalAmountOptions: { currency: 'rub', units: 0, nano: 0 },
  totalAmountSp: { currency: 'rub', units: 0, nano: 0 },
  totalAmountPortfolio: { currency: 'rub', units: 382, nano: ********* },
  virtualPositions: [],
  dailyYield: { currency: 'rub', units: 0, nano: ********* },
  dailyYieldRelative: { units: 0, nano: ********* }
}
2025-08-26T13:23:05.034Z bot:provider portfolioPositions [
  {
    figi: 'RUB000UTSTOM',
    instrumentType: 'currency',
    quantity: { units: -367, nano: -********* },
    averagePositionPrice: { currency: 'rub', units: 1, nano: 0 },
    expectedYield: { units: 0, nano: 0 },
    currentNkd: undefined,
    averagePositionPricePt: { units: 0, nano: 0 },
    currentPrice: { currency: 'rub', units: 1, nano: 0 },
    averagePositionPriceFifo: { currency: 'rub', units: 1, nano: 0 },
    quantityLots: { units: -367, nano: -********* },
    blocked: false,
    blockedLots: { units: 0, nano: 0 },
    positionUid: '33e24a92-aab0-409c-88b8-f2d57415b920',
    instrumentUid: 'a92e2e25-a698-45cc-a781-167cf465257c',
    varMargin: { currency: '', units: 0, nano: 0 },
    expectedYieldFifo: { units: 0, nano: 0 },
    dailyYield: { currency: 'rub', units: 0, nano: 0 }
  },
  {
    figi: 'TCS10A107563',
    instrumentType: 'etf',
    quantity: { units: 12, nano: 0 },
    averagePositionPrice: { currency: 'rub', units: 10, nano: 640000000 },
    expectedYield: { units: 0, nano: -********* },
    currentNkd: undefined,
    averagePositionPricePt: { units: 0, nano: 0 },
    currentPrice: { currency: 'rub', units: 10, nano: ********* },
    averagePositionPriceFifo: { currency: 'rub', units: 10, nano: 610000000 },
    quantityLots: { units: 12, nano: 0 },
    blocked: false,
    blockedLots: { units: 0, nano: 0 },
    positionUid: '8f747dc6-21bf-4f3a-878e-834880728a29',
    instrumentUid: 'd5cba263-cda7-440c-a21d-134fb5d334f6',
    varMargin: { currency: '', units: 0, nano: 0 },
    expectedYieldFifo: { units: 0, nano: -********* },
    dailyYield: { currency: 'rub', units: 0, nano: ********* }
  },
  {
    figi: 'TCS80A101X50',
    instrumentType: 'etf',
    quantity: { units: 12, nano: 0 },
    averagePositionPrice: { currency: 'rub', units: 10, nano: ********* },
    expectedYield: { units: 0, nano: 500000000 },
    currentNkd: undefined,
    averagePositionPricePt: { units: 0, nano: 0 },
    currentPrice: { currency: 'rub', units: 10, nano: ********* },
    averagePositionPriceFifo: { currency: 'rub', units: 10, nano: 760000000 },
    quantityLots: { units: 12, nano: 0 },
    blocked: false,
    blockedLots: { units: 0, nano: 0 },
    positionUid: '46dd8924-24e9-4581-afd2-97e0ca89009c',
    instrumentUid: 'de82be66-3b9b-4612-9572-61e3c6039013',
    varMargin: { currency: '', units: 0, nano: 0 },
    expectedYieldFifo: { units: 0, nano: 500000000 },
    dailyYield: { currency: 'rub', units: 0, nano: 440000000 }
  },
  {
    figi: 'BBG000000001',
    instrumentType: 'etf',
    quantity: { units: 14, nano: 0 },
    averagePositionPrice: { currency: 'rub', units: 9, nano: ********* },
    expectedYield: { units: 0, nano: -180000000 },
    currentNkd: undefined,
    averagePositionPricePt: { units: 0, nano: 0 },
    currentPrice: { currency: 'rub', units: 9, nano: ********* },
    averagePositionPriceFifo: { currency: 'rub', units: 9, nano: 310000000 },
    quantityLots: { units: 14, nano: 0 },
    blocked: false,
    blockedLots: { units: 0, nano: 0 },
    positionUid: '8005e2ec-66b3-49ae-9711-a424d9c9b61b',
    instrumentUid: 'e2d0dbac-d354-4c36-a5ed-e5aae42ffc76',
    varMargin: { currency: '', units: 0, nano: 0 },
    expectedYieldFifo: { units: 0, nano: -180000000 },
    dailyYield: { currency: 'rub', units: 0, nano: 280000000 }
  },
  {
    figi: 'TCS60A101X76',
    instrumentType: 'etf',
    quantity: { units: 19, nano: 0 },
    averagePositionPrice: { currency: 'rub', units: 6, nano: ********* },
    expectedYield: { units: 0, nano: -500000000 },
    currentNkd: undefined,
    averagePositionPricePt: { units: 0, nano: 0 },
    currentPrice: { currency: 'rub', units: 6, nano: ********* },
    averagePositionPriceFifo: { currency: 'rub', units: 6, nano: 720000000 },
    quantityLots: { units: 19, nano: 0 },
    blocked: false,
    blockedLots: { units: 0, nano: 0 },
    positionUid: 'ffd48f38-aa9b-491c-8e40-140a3f0bc931',
    instrumentUid: 'f509af83-6e71-462f-901f-bcb073f6773b',
    varMargin: { currency: '', units: 0, nano: 0 },
    expectedYieldFifo: { units: 0, nano: -500000000 },
    dailyYield: { currency: 'rub', units: 0, nano: -10000000 }
  },
  {
    figi: 'TCS00A10B0G9',
    instrumentType: 'etf',
    quantity: { units: 24, nano: 0 },
    averagePositionPrice: { currency: 'rub', units: 9, nano: 960000000 },
    expectedYield: { units: -1, nano: -********* },
    currentNkd: undefined,
    averagePositionPricePt: { units: 0, nano: 0 },
    currentPrice: { currency: 'rub', units: 9, nano: ********* },
    averagePositionPriceFifo: { currency: 'rub', units: 9, nano: 950000000 },
    quantityLots: { units: 24, nano: 0 },
    blocked: false,
    blockedLots: { units: 0, nano: 0 },
    positionUid: '0599cd56-02b0-4ceb-8045-eae66523979e',
    instrumentUid: 'fa20b747-e414-4922-963a-d19b752535b2',
    varMargin: { currency: '', units: 0, nano: 0 },
    expectedYieldFifo: { units: -1, nano: -********* },
    dailyYield: { currency: 'rub', units: 0, nano: -240000000 }
  }
]
2025-08-26T13:23:05.035Z bot:provider Getting positions
2025-08-26T13:23:05.270Z bot:provider positions {
  money: [ { currency: 'rub', units: -367, nano: -********* } ],
  blocked: [],
  securities: [
    {
      figi: 'BBG000000001',
      blocked: 0,
      balance: 14,
      positionUid: '8005e2ec-66b3-49ae-9711-a424d9c9b61b',
      instrumentUid: 'e2d0dbac-d354-4c36-a5ed-e5aae42ffc76',
      exchangeBlocked: false,
      instrumentType: 'etf'
    },
    {
      figi: 'TCS60A101X76',
      blocked: 0,
      balance: 19,
      positionUid: 'ffd48f38-aa9b-491c-8e40-140a3f0bc931',
      instrumentUid: 'f509af83-6e71-462f-901f-bcb073f6773b',
      exchangeBlocked: false,
      instrumentType: 'etf'
    },
    {
      figi: 'TCS10A107563',
      blocked: 0,
      balance: 12,
      positionUid: '8f747dc6-21bf-4f3a-878e-834880728a29',
      instrumentUid: 'd5cba263-cda7-440c-a21d-134fb5d334f6',
      exchangeBlocked: false,
      instrumentType: 'etf'
    },
    {
      figi: 'TCS00A10B0G9',
      blocked: 0,
      balance: 24,
      positionUid: '0599cd56-02b0-4ceb-8045-eae66523979e',
      instrumentUid: 'fa20b747-e414-4922-963a-d19b752535b2',
      exchangeBlocked: false,
      instrumentType: 'etf'
    },
    {
      figi: 'TCS80A101X50',
      blocked: 0,
      balance: 12,
      positionUid: '46dd8924-24e9-4581-afd2-97e0ca89009c',
      instrumentUid: 'de82be66-3b9b-4612-9572-61e3c6039013',
      exchangeBlocked: false,
      instrumentType: 'etf'
    }
  ],
  limitsLoadingInProgress: false,
  futures: [],
  options: [],
  accountId: '**********'
}
2025-08-26T13:23:05.271Z bot:provider Adding currencies to Wallet
2025-08-26T13:23:05.271Z bot:provider corePosition {
  pair: 'RUB/RUB',
  base: 'RUB',
  quote: 'RUB',
  figi: undefined,
  amount: -367,
  lotSize: 1,
  price: { units: 1, nano: 0 },
  priceNumber: 1,
  lotPrice: { units: 1, nano: 0 }
}
2025-08-26T13:23:05.274Z bot:provider Adding positions to Wallet
2025-08-26T13:23:05.274Z bot:provider position {
  figi: 'RUB000UTSTOM',
  instrumentType: 'currency',
  quantity: { units: -367, nano: -********* },
  averagePositionPrice: { currency: 'rub', units: 1, nano: 0 },
  expectedYield: { units: 0, nano: 0 },
  currentNkd: undefined,
  averagePositionPricePt: { units: 0, nano: 0 },
  currentPrice: { currency: 'rub', units: 1, nano: 0 },
  averagePositionPriceFifo: { currency: 'rub', units: 1, nano: 0 },
  quantityLots: { units: -367, nano: -********* },
  blocked: false,
  blockedLots: { units: 0, nano: 0 },
  positionUid: '33e24a92-aab0-409c-88b8-f2d57415b920',
  instrumentUid: 'a92e2e25-a698-45cc-a781-167cf465257c',
  varMargin: { currency: '', units: 0, nano: 0 },
  expectedYieldFifo: { units: 0, nano: 0 },
  dailyYield: { currency: 'rub', units: 0, nano: 0 }
}
2025-08-26T13:23:05.277Z bot:provider instrument undefined
2025-08-26T13:23:05.277Z bot:provider instrument not found by figi, skip position RUB000UTSTOM
2025-08-26T13:23:05.277Z bot:provider position {
  figi: 'TCS10A107563',
  instrumentType: 'etf',
  quantity: { units: 12, nano: 0 },
  averagePositionPrice: { currency: 'rub', units: 10, nano: 640000000 },
  expectedYield: { units: 0, nano: -********* },
  currentNkd: undefined,
  averagePositionPricePt: { units: 0, nano: 0 },
  currentPrice: { currency: 'rub', units: 10, nano: ********* },
  averagePositionPriceFifo: { currency: 'rub', units: 10, nano: 610000000 },
  quantityLots: { units: 12, nano: 0 },
  blocked: false,
  blockedLots: { units: 0, nano: 0 },
  positionUid: '8f747dc6-21bf-4f3a-878e-834880728a29',
  instrumentUid: 'd5cba263-cda7-440c-a21d-134fb5d334f6',
  varMargin: { currency: '', units: 0, nano: 0 },
  expectedYieldFifo: { units: 0, nano: -********* },
  dailyYield: { currency: 'rub', units: 0, nano: ********* }
}
2025-08-26T13:23:05.277Z bot:provider instrument {
  figi: 'TCS10A107563',
  ticker: 'TDIV@',
  classCode: 'SPBRU',
  isin: 'RU000A107563',
  lot: 1,
  currency: 'rub',
  klong: { units: 2, nano: 0 },
  kshort: { units: 2, nano: 0 },
  dlong: { units: 0, nano: 250000000 },
  dshort: { units: 0, nano: ********* },
  dlongMin: { units: 0, nano: ********* },
  dshortMin: { units: 0, nano: 350000000 },
  shortEnabledFlag: true,
  name: 'Дивидендные акции',
  exchange: 'spb_etf_ru_noweekend',
  fixedCommission: { units: 2, nano: 0 },
  focusType: 'equity',
  releasedDate: 2023-10-12T00:00:00.000Z,
  numShares: undefined,
  countryOfRisk: 'RU',
  countryOfRiskName: 'Российская Федерация',
  sector: 'other',
  rebalancingFreq: 'annual',
  tradingStatus: 5,
  otcFlag: false,
  buyAvailableFlag: true,
  sellAvailableFlag: true,
  minPriceIncrement: { units: 0, nano: 10000000 },
  apiTradeAvailableFlag: true,
  uid: 'd5cba263-cda7-440c-a21d-134fb5d334f6',
  realExchange: 2,
  positionUid: '8f747dc6-21bf-4f3a-878e-834880728a29',
  assetUid: 'a47c97b9-35b0-43f5-b4b7-05f42dfcaf67',
  instrumentExchange: 0,
  forIisFlag: true,
  forQualInvestorFlag: false,
  weekendFlag: false,
  blockedTcaFlag: false,
  liquidityFlag: true,
  first1minCandleDate: 2023-11-23T06:59:00.000Z,
  first1dayCandleDate: 2023-11-23T07:00:00.000Z,
  brand: {
    logoName: 'TDIV.png',
    logoBaseColor: '#000000',
    textColor: '#ffffff'
  },
  dlongClient: { units: 0, nano: ********* },
  dshortClient: { units: 0, nano: 350000000 }
}
2025-08-26T13:23:05.278Z bot:provider Getting last price
2025-08-26T13:23:05.581Z bot:provider lastPriceResult {
  lastPrices: [
    {
      figi: 'TCS10A107563',
      price: [Object],
      time: 2025-08-26T13:21:54.532Z,
      instrumentUid: 'd5cba263-cda7-440c-a21d-134fb5d334f6',
      lastPriceType: 1
    }
  ]
}
2025-08-26T13:23:05.581Z bot:provider lastPrice { units: 10, nano: ********* }
2025-08-26T13:23:10.584Z bot:provider priceWhenAddToWallet { units: 10, nano: ********* }
2025-08-26T13:23:10.585Z bot:provider corePosition {
  pair: 'TDIV@/RUB',
  base: 'TDIV@',
  quote: 'RUB',
  figi: 'TCS10A107563',
  amount: 12,
  lotSize: 1,
  price: { units: 10, nano: ********* },
  priceNumber: 10.54,
  lotPrice: { units: 10, nano: ********* },
  totalPrice: { units: 126, nano: ********* },
  totalPriceNumber: 126.47999999999999
}
2025-08-26T13:23:10.586Z bot:provider position {
  figi: 'TCS80A101X50',
  instrumentType: 'etf',
  quantity: { units: 12, nano: 0 },
  averagePositionPrice: { currency: 'rub', units: 10, nano: ********* },
  expectedYield: { units: 0, nano: 500000000 },
  currentNkd: undefined,
  averagePositionPricePt: { units: 0, nano: 0 },
  currentPrice: { currency: 'rub', units: 10, nano: ********* },
  averagePositionPriceFifo: { currency: 'rub', units: 10, nano: 760000000 },
  quantityLots: { units: 12, nano: 0 },
  blocked: false,
  blockedLots: { units: 0, nano: 0 },
  positionUid: '46dd8924-24e9-4581-afd2-97e0ca89009c',
  instrumentUid: 'de82be66-3b9b-4612-9572-61e3c6039013',
  varMargin: { currency: '', units: 0, nano: 0 },
  expectedYieldFifo: { units: 0, nano: 500000000 },
  dailyYield: { currency: 'rub', units: 0, nano: 440000000 }
}
2025-08-26T13:23:10.587Z bot:provider instrument {
  figi: 'TCS80A101X50',
  ticker: 'TGLD@',
  classCode: 'SPBRU',
  isin: 'RU000A101X50',
  lot: 1,
  currency: 'rub',
  klong: { units: 2, nano: 0 },
  kshort: { units: 2, nano: 0 },
  dlong: { units: 0, nano: 181800000 },
  dshort: { units: 0, nano: ********* },
  dlongMin: { units: 0, nano: 125000000 },
  dshortMin: { units: 0, nano: 166600000 },
  shortEnabledFlag: true,
  name: 'Золото',
  exchange: 'spb_etf_t',
  fixedCommission: { units: 2, nano: 0 },
  focusType: 'alternative_investment',
  releasedDate: 2020-07-13T00:00:00.000Z,
  numShares: undefined,
  countryOfRisk: 'RU',
  countryOfRiskName: 'Российская Федерация',
  sector: '',
  rebalancingFreq: '',
  tradingStatus: 5,
  otcFlag: false,
  buyAvailableFlag: true,
  sellAvailableFlag: true,
  minPriceIncrement: { units: 0, nano: 10000000 },
  apiTradeAvailableFlag: true,
  uid: 'de82be66-3b9b-4612-9572-61e3c6039013',
  realExchange: 2,
  positionUid: '46dd8924-24e9-4581-afd2-97e0ca89009c',
  assetUid: '1ca14ff7-ab31-4657-9303-b0455a1290cb',
  instrumentExchange: 0,
  forIisFlag: true,
  forQualInvestorFlag: false,
  weekendFlag: false,
  blockedTcaFlag: false,
  liquidityFlag: true,
  first1minCandleDate: 2021-08-25T07:02:00.000Z,
  first1dayCandleDate: 2021-08-25T07:00:00.000Z,
  brand: {
    logoName: 'TGLD.png',
    logoBaseColor: '#000000',
    textColor: '#ffffff'
  },
  dlongClient: { units: 0, nano: 125000000 },
  dshortClient: { units: 0, nano: 166600000 }
}
2025-08-26T13:23:10.588Z bot:provider Getting last price
2025-08-26T13:23:10.810Z bot:provider lastPriceResult {
  lastPrices: [
    {
      figi: 'TCS80A101X50',
      price: [Object],
      time: 2025-08-26T13:22:52.841Z,
      instrumentUid: 'de82be66-3b9b-4612-9572-61e3c6039013',
      lastPriceType: 1
    }
  ]
}
2025-08-26T13:23:10.811Z bot:provider lastPrice { units: 10, nano: ********* }
2025-08-26T13:23:15.812Z bot:provider priceWhenAddToWallet { units: 10, nano: ********* }
2025-08-26T13:23:15.813Z bot:provider corePosition {
  pair: 'TGLD@/RUB',
  base: 'TGLD@',
  quote: 'RUB',
  figi: 'TCS80A101X50',
  amount: 12,
  lotSize: 1,
  price: { units: 10, nano: ********* },
  priceNumber: 10.8,
  lotPrice: { units: 10, nano: ********* },
  totalPrice: { units: 129, nano: ********* },
  totalPriceNumber: 129.*********00002
}
2025-08-26T13:23:15.813Z bot:provider position {
  figi: 'BBG000000001',
  instrumentType: 'etf',
  quantity: { units: 14, nano: 0 },
  averagePositionPrice: { currency: 'rub', units: 9, nano: ********* },
  expectedYield: { units: 0, nano: -180000000 },
  currentNkd: undefined,
  averagePositionPricePt: { units: 0, nano: 0 },
  currentPrice: { currency: 'rub', units: 9, nano: ********* },
  averagePositionPriceFifo: { currency: 'rub', units: 9, nano: 310000000 },
  quantityLots: { units: 14, nano: 0 },
  blocked: false,
  blockedLots: { units: 0, nano: 0 },
  positionUid: '8005e2ec-66b3-49ae-9711-a424d9c9b61b',
  instrumentUid: 'e2d0dbac-d354-4c36-a5ed-e5aae42ffc76',
  varMargin: { currency: '', units: 0, nano: 0 },
  expectedYieldFifo: { units: 0, nano: -180000000 },
  dailyYield: { currency: 'rub', units: 0, nano: 280000000 }
}
2025-08-26T13:23:15.814Z bot:provider instrument {
  figi: 'BBG000000001',
  ticker: 'TRUR',
  classCode: 'TQTF',
  isin: 'RU000A1011U5',
  lot: 1,
  currency: 'rub',
  klong: { units: 2, nano: 0 },
  kshort: { units: 2, nano: 0 },
  dlong: { units: 0, nano: 181800000 },
  dshort: { units: 0, nano: ********* },
  dlongMin: { units: 0, nano: 125000000 },
  dshortMin: { units: 0, nano: 142800000 },
  shortEnabledFlag: true,
  name: 'Вечный портфель',
  exchange: 'moex_etf',
  fixedCommission: { units: 2, nano: 0 },
  focusType: 'mixed_allocation',
  releasedDate: 2019-11-07T00:00:00.000Z,
  numShares: undefined,
  countryOfRisk: 'RU',
  countryOfRiskName: 'Российская Федерация',
  sector: '',
  rebalancingFreq: 'annual',
  tradingStatus: 5,
  otcFlag: false,
  buyAvailableFlag: true,
  sellAvailableFlag: true,
  minPriceIncrement: { units: 0, nano: 10000000 },
  apiTradeAvailableFlag: true,
  uid: 'e2d0dbac-d354-4c36-a5ed-e5aae42ffc76',
  realExchange: 1,
  positionUid: '8005e2ec-66b3-49ae-9711-a424d9c9b61b',
  assetUid: 'df43c28e-9523-4ee2-8383-9fdb88b49e34',
  instrumentExchange: 0,
  forIisFlag: true,
  forQualInvestorFlag: false,
  weekendFlag: false,
  blockedTcaFlag: false,
  liquidityFlag: true,
  first1minCandleDate: 2019-12-09T07:11:00.000Z,
  first1dayCandleDate: 2019-12-09T07:00:00.000Z,
  brand: {
    logoName: 'TRUR.png',
    logoBaseColor: '#000000',
    textColor: '#ffffff'
  },
  dlongClient: { units: 0, nano: 125000000 },
  dshortClient: { units: 0, nano: 142800000 }
}
2025-08-26T13:23:15.815Z bot:provider Getting last price
2025-08-26T13:23:16.070Z bot:provider lastPriceResult {
  lastPrices: [
    {
      figi: 'BBG000000001',
      price: [Object],
      time: 2025-08-26T13:23:15.907Z,
      instrumentUid: 'e2d0dbac-d354-4c36-a5ed-e5aae42ffc76',
      lastPriceType: 1
    }
  ]
}
2025-08-26T13:23:16.070Z bot:provider lastPrice { units: 9, nano: ********* }
2025-08-26T13:23:21.073Z bot:provider priceWhenAddToWallet { units: 9, nano: ********* }
2025-08-26T13:23:21.073Z bot:provider corePosition {
  pair: 'TRUR/RUB',
  base: 'TRUR',
  quote: 'RUB',
  figi: 'BBG000000001',
  amount: 14,
  lotSize: 1,
  price: { units: 9, nano: ********* },
  priceNumber: 9.3,
  lotPrice: { units: 9, nano: ********* },
  totalPrice: { units: 130, nano: ********* },
  totalPriceNumber: 130.*********00002
}
2025-08-26T13:23:21.074Z bot:provider position {
  figi: 'TCS60A101X76',
  instrumentType: 'etf',
  quantity: { units: 19, nano: 0 },
  averagePositionPrice: { currency: 'rub', units: 6, nano: ********* },
  expectedYield: { units: 0, nano: -500000000 },
  currentNkd: undefined,
  averagePositionPricePt: { units: 0, nano: 0 },
  currentPrice: { currency: 'rub', units: 6, nano: ********* },
  averagePositionPriceFifo: { currency: 'rub', units: 6, nano: 720000000 },
  quantityLots: { units: 19, nano: 0 },
  blocked: false,
  blockedLots: { units: 0, nano: 0 },
  positionUid: 'ffd48f38-aa9b-491c-8e40-140a3f0bc931',
  instrumentUid: 'f509af83-6e71-462f-901f-bcb073f6773b',
  varMargin: { currency: '', units: 0, nano: 0 },
  expectedYieldFifo: { units: 0, nano: -500000000 },
  dailyYield: { currency: 'rub', units: 0, nano: -10000000 }
}
2025-08-26T13:23:21.076Z bot:provider instrument {
  figi: 'TCS60A101X76',
  ticker: 'TMOS@',
  classCode: 'SPBRU',
  isin: 'RU000A101X76',
  lot: 1,
  currency: 'rub',
  klong: { units: 2, nano: 0 },
  kshort: { units: 2, nano: 0 },
  dlong: { units: 0, nano: 181800000 },
  dshort: { units: 0, nano: ********* },
  dlongMin: { units: 0, nano: 125000000 },
  dshortMin: { units: 0, nano: 142800000 },
  shortEnabledFlag: true,
  name: 'Крупнейшие компании РФ',
  exchange: 'spb_etf_ru_noweekend',
  fixedCommission: { units: 0, nano: ********* },
  focusType: 'equity',
  releasedDate: 2020-07-13T00:00:00.000Z,
  numShares: undefined,
  countryOfRisk: 'RU',
  countryOfRiskName: 'Российская Федерация',
  sector: 'other',
  rebalancingFreq: 'quarterly',
  tradingStatus: 5,
  otcFlag: false,
  buyAvailableFlag: true,
  sellAvailableFlag: true,
  minPriceIncrement: { units: 0, nano: 10000000 },
  apiTradeAvailableFlag: true,
  uid: 'f509af83-6e71-462f-901f-bcb073f6773b',
  realExchange: 2,
  positionUid: 'ffd48f38-aa9b-491c-8e40-140a3f0bc931',
  assetUid: '41ee95a0-318f-49e1-bfe4-2c3f7c83fe21',
  instrumentExchange: 0,
  forIisFlag: true,
  forQualInvestorFlag: false,
  weekendFlag: false,
  blockedTcaFlag: false,
  liquidityFlag: true,
  first1minCandleDate: 2020-08-26T07:14:00.000Z,
  first1dayCandleDate: 2020-08-26T07:00:00.000Z,
  brand: {
    logoName: 'TMOS.png',
    logoBaseColor: '#000000',
    textColor: '#ffffff'
  },
  dlongClient: { units: 0, nano: 125000000 },
  dshortClient: { units: 0, nano: 142800000 }
}
2025-08-26T13:23:21.076Z bot:provider Getting last price
2025-08-26T13:23:21.310Z bot:provider lastPriceResult {
  lastPrices: [
    {
      figi: 'TCS60A101X76',
      price: [Object],
      time: 2025-08-26T13:23:21.167Z,
      instrumentUid: 'f509af83-6e71-462f-901f-bcb073f6773b',
      lastPriceType: 1
    }
  ]
}
2025-08-26T13:23:21.310Z bot:provider lastPrice { units: 6, nano: ********* }
2025-08-26T13:23:26.313Z bot:provider priceWhenAddToWallet { units: 6, nano: ********* }
2025-08-26T13:23:26.313Z bot:provider corePosition {
  pair: 'TMOS@/RUB',
  base: 'TMOS@',
  quote: 'RUB',
  figi: 'TCS60A101X76',
  amount: 19,
  lotSize: 1,
  price: { units: 6, nano: ********* },
  priceNumber: 6.69,
  lotPrice: { units: 6, nano: ********* },
  totalPrice: { units: 127, nano: ********* },
  totalPriceNumber: 127.*********00001
}
2025-08-26T13:23:26.313Z bot:provider position {
  figi: 'TCS00A10B0G9',
  instrumentType: 'etf',
  quantity: { units: 24, nano: 0 },
  averagePositionPrice: { currency: 'rub', units: 9, nano: 960000000 },
  expectedYield: { units: -1, nano: -********* },
  currentNkd: undefined,
  averagePositionPricePt: { units: 0, nano: 0 },
  currentPrice: { currency: 'rub', units: 9, nano: ********* },
  averagePositionPriceFifo: { currency: 'rub', units: 9, nano: 950000000 },
  quantityLots: { units: 24, nano: 0 },
  blocked: false,
  blockedLots: { units: 0, nano: 0 },
  positionUid: '0599cd56-02b0-4ceb-8045-eae66523979e',
  instrumentUid: 'fa20b747-e414-4922-963a-d19b752535b2',
  varMargin: { currency: '', units: 0, nano: 0 },
  expectedYieldFifo: { units: -1, nano: -********* },
  dailyYield: { currency: 'rub', units: 0, nano: -240000000 }
}
2025-08-26T13:23:26.315Z bot:provider instrument {
  figi: 'TCS00A10B0G9',
  ticker: 'TRND',
  classCode: 'TQTF',
  isin: 'RU000A10B0G9',
  lot: 1,
  currency: 'rub',
  klong: { units: 2, nano: 0 },
  kshort: { units: 2, nano: 0 },
  dlong: { units: 0, nano: 350000000 },
  dshort: undefined,
  dlongMin: { units: 0, nano: ********* },
  dshortMin: undefined,
  shortEnabledFlag: false,
  name: 'Трендовые акции',
  exchange: 'MOEX_PLUS',
  fixedCommission: { units: 2, nano: 0 },
  focusType: 'equity',
  releasedDate: 2025-02-20T00:00:00.000Z,
  numShares: undefined,
  countryOfRisk: 'RU',
  countryOfRiskName: 'Российская Федерация',
  sector: 'other',
  rebalancingFreq: 'quarterly',
  tradingStatus: 5,
  otcFlag: false,
  buyAvailableFlag: true,
  sellAvailableFlag: true,
  minPriceIncrement: { units: 0, nano: 10000000 },
  apiTradeAvailableFlag: true,
  uid: 'fa20b747-e414-4922-963a-d19b752535b2',
  realExchange: 1,
  positionUid: '0599cd56-02b0-4ceb-8045-eae66523979e',
  assetUid: '63334cd2-9656-41f0-9889-8ef28b89e56e',
  instrumentExchange: 0,
  forIisFlag: true,
  forQualInvestorFlag: false,
  weekendFlag: false,
  blockedTcaFlag: false,
  liquidityFlag: true,
  first1minCandleDate: 2025-03-24T07:00:00.000Z,
  first1dayCandleDate: 2025-03-24T00:00:00.000Z,
  brand: {
    logoName: 'TRND.png',
    logoBaseColor: '#000000',
    textColor: '#ffffff'
  },
  dlongClient: { units: 0, nano: ********* },
  dshortClient: { units: 0, nano: 0 }
}
2025-08-26T13:23:26.315Z bot:provider Getting last price
2025-08-26T13:23:26.528Z bot:provider lastPriceResult {
  lastPrices: [
    {
      figi: 'TCS00A10B0G9',
      price: [Object],
      time: 2025-08-26T13:21:38.928Z,
      instrumentUid: 'fa20b747-e414-4922-963a-d19b752535b2',
      lastPriceType: 1
    }
  ]
}
2025-08-26T13:23:26.528Z bot:provider lastPrice { units: 9, nano: ********* }
2025-08-26T13:23:31.530Z bot:provider priceWhenAddToWallet { units: 9, nano: ********* }
2025-08-26T13:23:31.530Z bot:provider corePosition {
  pair: 'TRND/RUB',
  base: 'TRND',
  quote: 'RUB',
  figi: 'TCS00A10B0G9',
  amount: 24,
  lotSize: 1,
  price: { units: 9, nano: ********* },
  priceNumber: 9.88,
  lotPrice: { units: 9, nano: ********* },
  totalPrice: { units: 237, nano: ********* },
  totalPriceNumber: 237.12
}
2025-08-26T13:23:31.532Z bot:provider [
  {
    pair: 'RUB/RUB',
    base: 'RUB',
    quote: 'RUB',
    figi: undefined,
    amount: -367,
    lotSize: 1,
    price: { units: 1, nano: 0 },
    priceNumber: 1,
    lotPrice: { units: 1, nano: 0 }
  },
  {
    pair: 'TDIV@/RUB',
    base: 'TDIV@',
    quote: 'RUB',
    figi: 'TCS10A107563',
    amount: 12,
    lotSize: 1,
    price: { units: 10, nano: ********* },
    priceNumber: 10.54,
    lotPrice: { units: 10, nano: ********* },
    totalPrice: { units: 126, nano: ********* },
    totalPriceNumber: 126.47999999999999
  },
  {
    pair: 'TGLD@/RUB',
    base: 'TGLD@',
    quote: 'RUB',
    figi: 'TCS80A101X50',
    amount: 12,
    lotSize: 1,
    price: { units: 10, nano: ********* },
    priceNumber: 10.8,
    lotPrice: { units: 10, nano: ********* },
    totalPrice: { units: 129, nano: ********* },
    totalPriceNumber: 129.*********00002
  },
  {
    pair: 'TRUR/RUB',
    base: 'TRUR',
    quote: 'RUB',
    figi: 'BBG000000001',
    amount: 14,
    lotSize: 1,
    price: { units: 9, nano: ********* },
    priceNumber: 9.3,
    lotPrice: { units: 9, nano: ********* },
    totalPrice: { units: 130, nano: ********* },
    totalPriceNumber: 130.*********00002
  },
  {
    pair: 'TMOS@/RUB',
    base: 'TMOS@',
    quote: 'RUB',
    figi: 'TCS60A101X76',
    amount: 19,
    lotSize: 1,
    price: { units: 6, nano: ********* },
    priceNumber: 6.69,
    lotPrice: { units: 6, nano: ********* },
    totalPrice: { units: 127, nano: ********* },
    totalPriceNumber: 127.*********00001
  },
  {
    pair: 'TRND/RUB',
    base: 'TRND',
    quote: 'RUB',
    figi: 'TCS00A10B0G9',
    amount: 24,
    lotSize: 1,
    price: { units: 9, nano: ********* },
    priceNumber: 9.88,
    lotPrice: { units: 9, nano: ********* },
    totalPrice: { units: 237, nano: ********* },
    totalPriceNumber: 237.12
  }
]
[etfCap] buildAumMapSmart: fetched HTML length=241430
[etfCap] buildAumMapSmart: auto result: {}
[etfCap] [DEBUG] TBRU AUM search: FOUND
[etfCap] [DEBUG] TDIV AUM search: FOUND
[etfCap] [DEBUG] TITR AUM search: FOUND
[etfCap] [DEBUG] TMON AUM search: FOUND
[etfCap] [DEBUG] TMOS AUM search: FOUND
[etfCap] [DEBUG] TOFZ AUM search: FOUND
[etfCap] buildAumMapSmart: final result: {
  TPAY: {
    amount: 22858776670.47,
    currency: "RUB",
  },
  TGLD: {
    amount: 113027064.99,
    currency: "USD",
  },
  TRUR: {
    amount: 16944126813.56,
    currency: "RUB",
  },
  TRND: {
    amount: *********.84,
    currency: "RUB",
  },
  TBRU: {
    amount: 5055918283.76,
    currency: "RUB",
  },
  TDIV: {
    amount: 1011270453.78,
    currency: "RUB",
  },
  TITR: {
    amount: 451284431.12,
    currency: "RUB",
  },
  TLCB: {
    amount: 10915848062.94,
    currency: "RUB",
  },
  TMON: {
    amount: 205183053216,
    currency: "RUB",
  },
  TMOS: {
    amount: 12288910124.98,
    currency: "RUB",
  },
  TOFZ: {
    amount: 3654379925.22,
    currency: "RUB",
  },
}
[pollEtfMetrics] symbol=TPAY search sharesCount via Smartfeed API
[pollEtfMetrics] smartfeed brand=Пассивный доход news=50 cursorNext=ts:1753963176077…
[pollEtfMetrics] smartfeed brand=Пассивный доход news=50 cursorNext=ts:1751289429669…
[pollEtfMetrics] smartfeed brand=Пассивный доход news=50 cursorNext=ts:1748465649591…
[pollEtfMetrics] smartfeed hit id=18548236 title=Количество паев в обращении уменьшилось count=225600000
[pollEtfMetrics] price: lastPriceRUB=101.69 for TPAY
[pollEtfMetrics] payload TPAY: price=101.69 shares=225600000 aum=22858776670.47 mcap=22941264000
[pollEtfMetrics] saved /Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/etf_metrics/TPAY.json
[pollEtfMetrics] symbol=TGLD search sharesCount via Smartfeed API
[pollEtfMetrics] smartfeed brand=Золото news=50 cursorNext=ts:1754497092000…
[pollEtfMetrics] smartfeed brand=Золото news=50 cursorNext=ts:1753341720000…
[pollEtfMetrics] smartfeed hit id=18548894 title=Количество паев в обращении уменьшилось count=843600000
[pollEtfMetrics] price: lastPriceRUB=10.79 for TGLD
[pollEtfMetrics] payload TGLD: price=10.79 shares=843600000 aum=9119498317.066158 mcap=9102444000
[pollEtfMetrics] saved /Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/etf_metrics/TGLD.json
[pollEtfMetrics] symbol=TRUR search sharesCount via Smartfeed API
[pollEtfMetrics] smartfeed brand=Вечный портфель news=50 cursorNext=ts:1748984045726…
[pollEtfMetrics] smartfeed hit id=18549024 title=В фонд поступили новые деньги count=1818500000
[pollEtfMetrics] price: lastPriceRUB=9.29 for TRUR
[pollEtfMetrics] payload TRUR: price=9.29 shares=1818500000 aum=16944126813.56 mcap=16893864999.999998
[pollEtfMetrics] saved /Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/etf_metrics/TRUR.json
[pollEtfMetrics] symbol=TRND search sharesCount via Smartfeed API
[pollEtfMetrics] smartfeed brand=Трендовые акции news=0 cursorNext=null
[pollEtfMetrics] symbol=TRND sharesCount from local cache: ********
[pollEtfMetrics] price: lastPriceRUB=9.88 for TRND
[pollEtfMetrics] payload TRND: price=9.88 shares=******** aum=*********.84 mcap=*********.********
[pollEtfMetrics] saved /Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/etf_metrics/TRND.json
[pollEtfMetrics] symbol=TBRU search sharesCount via Smartfeed API
[pollEtfMetrics] smartfeed brand=Российские облигации news=50 cursorNext=ts:*************…
[pollEtfMetrics] [DEBUG] Parsing API: https://www.tbank.ru/api/invest/smartfeed-public/v1/feed/api/brands/%D0%A0%D0%BE%D1%81%D1%81%D0%B8%D0%B9%D1%81%D0%BA%D0%B8%D0%B5%20%D0%BE%D0%B1%D0%BB%D0%B8%D0%B3%D0%B0%D1%86%D0%B8%D0%B8/fund-news?limit=50
[pollEtfMetrics] [DEBUG] Found 50 news items
[pollEtfMetrics] [DEBUG] News 1: id=********, title="В фонд поступили новые деньги"
[pollEtfMetrics] [DEBUG] Additional fields: [{"name":"Дата выдачи паев","value":"26.08.2025"},{"name":"Выпущено паев","value":"4,7 млн шт."},{"name":"На сумму","value":"35 млн ₽"},{"name":"Всего паев","value":"702,1 млн шт."},{"name":"Цена пая","value":"7,3839 ₽"}]
[pollEtfMetrics] [DEBUG] News 2: id=********, title="Фонд получил купон по облигациям Гидромашсервис 001Р-04"
[pollEtfMetrics] [DEBUG] Additional fields: [{"name":"Тип дохода","value":"Купон"},{"name":"Сумма дохода","value":"1,3 млн ₽"},{"name":"Доля фонда","value":"0,03 %"}]
[pollEtfMetrics] [DEBUG] News 3: id=18549099, title="Фонд получил купон по облигациям Софтлайн выпуск 002Р-01"
[pollEtfMetrics] [DEBUG] Additional fields: [{"name":"Тип дохода","value":"Купон"},{"name":"Сумма дохода","value":"868,5 тыс ₽"},{"name":"Доля фонда","value":"0,02 %"}]
[pollEtfMetrics] [DEBUG] News 4: id=18549096, title="Совершены сделки с ценными бумагами в составе фонда"
[pollEtfMetrics] [DEBUG] News 5: id=18549090, title="В фонд поступили новые деньги"
[pollEtfMetrics] [DEBUG] Additional fields: [{"name":"Дата выдачи паев","value":"25.08.2025"},{"name":"Выпущено паев","value":"12,2 млн шт."},{"name":"На сумму","value":"90 млн ₽"},{"name":"Всего паев","value":"697,4 млн шт."},{"name":"Цена пая","value":"7,3787 ₽"}]
[pollEtfMetrics] smartfeed hit id=******** title=В фонд поступили новые деньги count=702100000
[pollEtfMetrics] [DEBUG] AUM search for TBRU:
[pollEtfMetrics] [DEBUG] AUM map entry: {"amount":5055918283.76,"currency":"RUB"}
[pollEtfMetrics] [DEBUG] AUM result: found
[pollEtfMetrics] price: lastPriceRUB=7.39 for TBRU
[pollEtfMetrics] payload TBRU: price=7.39 shares=702100000 aum=5055918283.76 mcap=5188519000
[pollEtfMetrics] saved /Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/etf_metrics/TBRU.json
[pollEtfMetrics] symbol=TDIV search sharesCount via Smartfeed API
[pollEtfMetrics] smartfeed brand=Дивидендные акции news=50 cursorNext=ts:1753365997104…
[pollEtfMetrics] smartfeed hit id=18548929 title=Количество паев в обращении уменьшилось count=95500000
[pollEtfMetrics] price: lastPriceRUB=10.54 for TDIV
[pollEtfMetrics] payload TDIV: price=10.54 shares=95500000 aum=1011270453.78 mcap=1006569999.9999999
[pollEtfMetrics] saved /Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/etf_metrics/TDIV.json
[pollEtfMetrics] symbol=TITR search sharesCount via Smartfeed API
[pollEtfMetrics] smartfeed brand=Российские Технологии news=50 cursorNext=ts:1749213928249…
[pollEtfMetrics] smartfeed hit id=18549061 title=Количество паев в обращении уменьшилось count=66900000
[pollEtfMetrics] price: lastPriceRUB=6.66 for TITR
[pollEtfMetrics] payload TITR: price=6.66 shares=66900000 aum=451284431.12 mcap=445554000
[pollEtfMetrics] saved /Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/etf_metrics/TITR.json
[pollEtfMetrics] symbol=TLCB search sharesCount via Smartfeed API
[pollEtfMetrics] smartfeed brand=Локальные валютные облигации news=50 cursorNext=ts:1753876700507…
[pollEtfMetrics] smartfeed hit id=18549078 title=В фонд поступили новые деньги count=1079400000
[pollEtfMetrics] price: lastPriceRUB=10.15 for TLCB
[pollEtfMetrics] payload TLCB: price=10.15 shares=1079400000 aum=10915848062.94 mcap=10955910000
[pollEtfMetrics] saved /Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/etf_metrics/TLCB.json
[pollEtfMetrics] symbol=TMON search sharesCount via Smartfeed API
[pollEtfMetrics] smartfeed brand=Денежный рынок news=50 cursorNext=ts:1750774986870…
[pollEtfMetrics] smartfeed hit id=18549094 title=В фонд поступили новые деньги count=1467400000
[pollEtfMetrics] price: lastPriceRUB=141.71 for TMON
[pollEtfMetrics] payload TMON: price=141.71 shares=1467400000 aum=205183053216 mcap=207945254000
[pollEtfMetrics] saved /Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/etf_metrics/TMON.json
[pollEtfMetrics] symbol=TMOS search sharesCount via Smartfeed API
[pollEtfMetrics] smartfeed brand=Крупнейшие компании РФ news=50 cursorNext=ts:1752008050878…
[pollEtfMetrics] smartfeed hit id=18549104 title=В фонд поступили новые деньги count=1844300000
[pollEtfMetrics] price: lastPriceRUB=6.69 for TMOS
[pollEtfMetrics] payload TMOS: price=6.69 shares=1844300000 aum=12288910124.98 mcap=12338367000
[pollEtfMetrics] saved /Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/etf_metrics/TMOS.json
[pollEtfMetrics] symbol=TOFZ search sharesCount via Smartfeed API
[pollEtfMetrics] smartfeed brand=Государственные облигации news=50 cursorNext=ts:1752846322262…
[pollEtfMetrics] smartfeed hit id=18549093 title=В фонд поступили новые деньги count=305800000
[pollEtfMetrics] price: lastPriceRUB=13.28 for TOFZ
[pollEtfMetrics] payload TOFZ: price=13.28 shares=305800000 aum=3654379925.22 mcap=4061024000
[pollEtfMetrics] saved /Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/etf_metrics/TOFZ.json
[pollEtfMetrics] symbol=TPAY search sharesCount via Smartfeed API
[pollEtfMetrics] smartfeed brand=Пассивный доход news=50 cursorNext=ts:1753963176077…
[pollEtfMetrics] smartfeed brand=Пассивный доход news=50 cursorNext=ts:1751289429669…
[pollEtfMetrics] smartfeed brand=Пассивный доход news=50 cursorNext=ts:1748465649591…
[pollEtfMetrics] smartfeed hit id=18548236 title=Количество паев в обращении уменьшилось count=225600000
[pollEtfMetrics] price: lastPriceRUB=101.69 for TPAY
[pollEtfMetrics] payload TPAY: price=101.69 shares=225600000 aum=22858776670.47 mcap=22941264000
[pollEtfMetrics] saved /Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/etf_metrics/TPAY.json
2025-08-26T13:24:23.191Z bot:balancer Margin strategy: {
  shouldRemoveMargin: false,
  reason: 'Not time to apply margin strategy',
  transferCost: 0,
  timeInfo: { timeToClose: 0, timeToNextBalance: 0, isLastBalance: false },
  marginPositions: [
    {
      pair: 'TDIV@/RUB',
      base: 'TDIV@',
      quote: 'RUB',
      figi: 'TCS10A107563',
      amount: 12,
      lotSize: 1,
      price: [Object],
      priceNumber: 10.54,
      lotPrice: [Object],
      totalPrice: [Object],
      totalPriceNumber: 126.47999999999999,
      isMargin: true,
      marginValue: 94.85999999999999,
      leverage: 4,
      marginCall: false
    },
    {
      pair: 'TGLD@/RUB',
      base: 'TGLD@',
      quote: 'RUB',
      figi: 'TCS80A101X50',
      amount: 12,
      lotSize: 1,
      price: [Object],
      priceNumber: 10.8,
      lotPrice: [Object],
      totalPrice: [Object],
      totalPriceNumber: 129.*********00002,
      isMargin: true,
      marginValue: 97.*********00002,
      leverage: 4,
      marginCall: false
    },
    {
      pair: 'TRUR/RUB',
      base: 'TRUR',
      quote: 'RUB',
      figi: 'BBG000000001',
      amount: 14,
      lotSize: 1,
      price: [Object],
      priceNumber: 9.3,
      lotPrice: [Object],
      totalPrice: [Object],
      totalPriceNumber: 130.*********00002,
      isMargin: true,
      marginValue: 97.65,
      leverage: 4,
      marginCall: false
    },
    {
      pair: 'TMOS@/RUB',
      base: 'TMOS@',
      quote: 'RUB',
      figi: 'TCS60A101X76',
      amount: 19,
      lotSize: 1,
      price: [Object],
      priceNumber: 6.69,
      lotPrice: [Object],
      totalPrice: [Object],
      totalPriceNumber: 127.*********00001,
      isMargin: true,
      marginValue: 95.33250000000001,
      leverage: 4,
      marginCall: false
    },
    {
      pair: 'TRND/RUB',
      base: 'TRND',
      quote: 'RUB',
      figi: 'TCS00A10B0G9',
      amount: 24,
      lotSize: 1,
      price: [Object],
      priceNumber: 9.88,
      lotPrice: [Object],
      totalPrice: [Object],
      totalPriceNumber: 237.12,
      isMargin: true,
      marginValue: 177.84,
      leverage: 4,
      marginCall: false
    }
  ]
}
2025-08-26T13:24:23.192Z bot:balancer Normalizing percentages to make total sum equal 100%, to exclude human factor
2025-08-26T13:24:23.192Z bot:balancer desiredWallet {
  TRAY: 7.945478786435518,
  TGLD: 3.1698450650156222,
  TRUR: 5.889606521495715,
  TRND: 0.07380370060943389,
  TBRU: 1.7573858850225417,
  TDIV: 0.35150734677057416,
  TITR: 0.15686188835926254,
  TLCB: 3.7942380062747123,
  TMON: 71.31954698038868,
  TMOS: 4.271500444403862,
  TOFZ: 1.270225375224085,
  TPAY: 7.945478786435518
}
2025-08-26T13:24:23.192Z bot:balancer desiredSum 107.94547878643552
2025-08-26T13:24:23.192Z bot:balancer normalizedDesire {
  TRAY: 7.360640645408811,
  TGLD: 2.936524160763597,
  TRUR: 5.456093750019853,
  TRND: 0.06837127542456008,
  TBRU: 1.6280310252728951,
  TDIV: 0.3256341541325812,
  TITR: 0.14531584844753487,
  TLCB: 3.5149577814013075,
  TMON: 66.06997141722876,
  TMOS: 3.9570906465242537,
  TOFZ: 1.1767286499670446,
  TPAY: 7.360640645408811
}
2025-08-26T13:24:23.192Z bot:balancer Adding missing instruments from portfolio to DesireWallet with value 0
2025-08-26T13:24:23.192Z bot:balancer RUB not found in desired portfolio, adding with value 0.
2025-08-26T13:24:23.193Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:24:23.193Z bot:balancer positionIndex -1
2025-08-26T13:24:23.193Z bot:balancer Ticker from DesireWallet not found in portfolio. Creating.
2025-08-26T13:24:23.196Z bot:balancer {
  figi: 'TCS00A108WX3',
  ticker: 'TPAY',
  classCode: 'TQTF',
  isin: 'RU000A108WX3',
  lot: 1,
  currency: 'rub',
  klong: { units: 2, nano: 0 },
  kshort: { units: 2, nano: 0 },
  dlong: { units: 0, nano: 166600000 },
  dshort: undefined,
  dlongMin: { units: 0, nano: 153800000 },
  dshortMin: undefined,
  shortEnabledFlag: false,
  name: 'Пассивный доход',
  exchange: 'moex_etf_evening',
  fixedCommission: { units: 0, nano: 900000000 },
  focusType: 'fixed_income',
  releasedDate: 2024-06-27T00:00:00.000Z,
  numShares: undefined,
  countryOfRisk: 'RU',
  countryOfRiskName: 'Российская Федерация',
  sector: 'other',
  rebalancingFreq: 'semi_annual',
  tradingStatus: 5,
  otcFlag: false,
  buyAvailableFlag: true,
  sellAvailableFlag: true,
  minPriceIncrement: { units: 0, nano: 10000000 },
  apiTradeAvailableFlag: true,
  uid: '1d0e01e5-148c-40e5-bb8f-1bf2d8e03c1a',
  realExchange: 1,
  positionUid: '41be4f89-9d19-4eac-9ae4-09276f85956a',
  assetUid: 'a9f74b0a-f3dc-4a25-aebe-7fb1732dd510',
  instrumentExchange: 0,
  forIisFlag: true,
  forQualInvestorFlag: false,
  weekendFlag: false,
  blockedTcaFlag: false,
  liquidityFlag: true,
  first1minCandleDate: 2024-08-12T06:59:00.000Z,
  first1dayCandleDate: 2024-08-12T00:00:00.000Z,
  brand: {
    logoName: 'TPAY.png',
    logoBaseColor: '#000000',
    textColor: '#ffffff'
  },
  dlongClient: { units: 0, nano: 153800000 },
  dshortClient: { units: 0, nano: 0 }
}
2025-08-26T13:24:23.196Z bot:balancer TCS00A108WX3
2025-08-26T13:24:23.196Z bot:balancer 1
2025-08-26T13:24:23.196Z bot:provider Getting last price
2025-08-26T13:24:23.415Z bot:provider lastPriceResult {
  lastPrices: [
    {
      figi: 'TCS00A108WX3',
      price: [Object],
      time: 2025-08-26T13:23:56.763Z,
      instrumentUid: '1d0e01e5-148c-40e5-bb8f-1bf2d8e03c1a',
      lastPriceType: 1
    }
  ]
}
2025-08-26T13:24:23.416Z bot:provider lastPrice { units: 101, nano: ********* }
2025-08-26T13:24:28.419Z bot:balancer newPosition {
  pair: 'TPAY/RUB',
  base: 'TPAY',
  quote: 'RUB',
  figi: 'TCS00A108WX3',
  price: { units: 101, nano: ********* },
  priceNumber: 101.69,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 101, nano: ********* }
}
2025-08-26T13:24:28.419Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:24:28.419Z bot:balancer positionIndex 2
2025-08-26T13:24:28.419Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:24:28.419Z bot:balancer positionIndex 3
2025-08-26T13:24:28.419Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:24:28.419Z bot:balancer positionIndex 5
2025-08-26T13:24:28.419Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:24:28.419Z bot:balancer positionIndex -1
2025-08-26T13:24:28.419Z bot:balancer Ticker from DesireWallet not found in portfolio. Creating.
2025-08-26T13:24:28.422Z bot:balancer {
  figi: 'TCS60A1039N1',
  ticker: 'TBRU@',
  classCode: 'SPBRU',
  isin: 'RU000A1039N1',
  lot: 1,
  currency: 'rub',
  klong: { units: 2, nano: 0 },
  kshort: { units: 2, nano: 0 },
  dlong: { units: 0, nano: ********* },
  dshort: { units: 0, nano: ********* },
  dlongMin: { units: 0, nano: 142800000 },
  dshortMin: { units: 0, nano: 142800000 },
  shortEnabledFlag: true,
  name: 'Российские облигации',
  exchange: 'spb_etf_ru_noweekend',
  fixedCommission: { units: 1, nano: 500000000 },
  focusType: 'fixed_income',
  releasedDate: 2021-06-10T00:00:00.000Z,
  numShares: undefined,
  countryOfRisk: 'RU',
  countryOfRiskName: 'Российская Федерация',
  sector: 'other',
  rebalancingFreq: 'semi_annual',
  tradingStatus: 5,
  otcFlag: false,
  buyAvailableFlag: true,
  sellAvailableFlag: true,
  minPriceIncrement: { units: 0, nano: 10000000 },
  apiTradeAvailableFlag: true,
  uid: 'e8acd2fb-6de6-4ea4-9bfb-0daad9b2ed7b',
  realExchange: 2,
  positionUid: '68f44dc7-7ad2-4246-b1ba-c039b7703371',
  assetUid: 'a327ef84-2aaa-4ffe-b71c-1cbeb9dadd0f',
  instrumentExchange: 0,
  forIisFlag: true,
  forQualInvestorFlag: false,
  weekendFlag: false,
  blockedTcaFlag: false,
  liquidityFlag: true,
  first1minCandleDate: 2021-07-12T07:00:00.000Z,
  first1dayCandleDate: 2021-07-12T07:00:00.000Z,
  brand: {
    logoName: 'TBRU.png',
    logoBaseColor: '#000000',
    textColor: '#ffffff'
  },
  dlongClient: { units: 0, nano: 142800000 },
  dshortClient: { units: 0, nano: 142800000 }
}
2025-08-26T13:24:28.422Z bot:balancer TCS60A1039N1
2025-08-26T13:24:28.422Z bot:balancer 1
2025-08-26T13:24:28.422Z bot:provider Getting last price
2025-08-26T13:24:28.640Z bot:provider lastPriceResult {
  lastPrices: [
    {
      figi: 'TCS60A1039N1',
      price: [Object],
      time: 2025-08-26T13:23:55.514Z,
      instrumentUid: 'e8acd2fb-6de6-4ea4-9bfb-0daad9b2ed7b',
      lastPriceType: 1
    }
  ]
}
2025-08-26T13:24:28.640Z bot:provider lastPrice { units: 7, nano: 390000000 }
2025-08-26T13:24:33.641Z bot:balancer newPosition {
  pair: 'TBRU/RUB',
  base: 'TBRU',
  quote: 'RUB',
  figi: 'TCS60A1039N1',
  price: { units: 7, nano: 390000000 },
  priceNumber: 7.39,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 7, nano: 390000000 }
}
2025-08-26T13:24:33.642Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:24:33.642Z bot:balancer positionIndex 1
2025-08-26T13:24:33.642Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:24:33.642Z bot:balancer positionIndex -1
2025-08-26T13:24:33.642Z bot:balancer Ticker from DesireWallet not found in portfolio. Creating.
2025-08-26T13:24:33.650Z bot:balancer {
  figi: 'TCS30A108BL2',
  ticker: 'TITR@',
  classCode: 'SPBRU',
  isin: 'RU000A108BL2',
  lot: 1,
  currency: 'rub',
  klong: { units: 2, nano: 0 },
  kshort: { units: 2, nano: 0 },
  dlong: { units: 0, nano: 250000000 },
  dshort: undefined,
  dlongMin: { units: 0, nano: ********* },
  dshortMin: undefined,
  shortEnabledFlag: false,
  name: 'Российские Технологии',
  exchange: 'spb_etf_t',
  fixedCommission: { units: 2, nano: 0 },
  focusType: 'mixed_allocation',
  releasedDate: 2024-04-18T00:00:00.000Z,
  numShares: undefined,
  countryOfRisk: 'RU',
  countryOfRiskName: 'Российская Федерация',
  sector: 'other',
  rebalancingFreq: 'quarterly',
  tradingStatus: 5,
  otcFlag: false,
  buyAvailableFlag: true,
  sellAvailableFlag: true,
  minPriceIncrement: { units: 0, nano: 10000000 },
  apiTradeAvailableFlag: true,
  uid: '*************-4254-9c78-255b0067fcbe',
  realExchange: 2,
  positionUid: '09a59e18-5be2-4eec-b149-aae369f49561',
  assetUid: 'a0c7e33e-e836-46bd-928b-2486852496d5',
  instrumentExchange: 0,
  forIisFlag: true,
  forQualInvestorFlag: false,
  weekendFlag: false,
  blockedTcaFlag: false,
  liquidityFlag: true,
  first1minCandleDate: 2024-05-20T07:06:00.000Z,
  first1dayCandleDate: 2024-05-20T00:00:00.000Z,
  brand: {
    logoName: 'TITR.png',
    logoBaseColor: '#000000',
    textColor: '#ffffff'
  },
  dlongClient: { units: 0, nano: ********* },
  dshortClient: { units: 0, nano: 0 }
}
2025-08-26T13:24:33.650Z bot:balancer TCS30A108BL2
2025-08-26T13:24:33.650Z bot:balancer 1
2025-08-26T13:24:33.650Z bot:provider Getting last price
2025-08-26T13:24:33.882Z bot:provider lastPriceResult {
  lastPrices: [
    {
      figi: 'TCS30A108BL2',
      price: [Object],
      time: 2025-08-26T13:22:18.632Z,
      instrumentUid: '*************-4254-9c78-255b0067fcbe',
      lastPriceType: 1
    }
  ]
}
2025-08-26T13:24:33.883Z bot:provider lastPrice { units: 6, nano: 660000000 }
2025-08-26T13:24:38.884Z bot:balancer newPosition {
  pair: 'TITR/RUB',
  base: 'TITR',
  quote: 'RUB',
  figi: 'TCS30A108BL2',
  price: { units: 6, nano: 660000000 },
  priceNumber: 6.66,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 6, nano: 660000000 }
}
2025-08-26T13:24:38.886Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:24:38.886Z bot:balancer positionIndex -1
2025-08-26T13:24:38.886Z bot:balancer Ticker from DesireWallet not found in portfolio. Creating.
2025-08-26T13:24:38.889Z bot:balancer {
  figi: 'TCS20A107597',
  ticker: 'TLCB@',
  classCode: 'SPBRU',
  isin: 'RU000A107597',
  lot: 1,
  currency: 'rub',
  klong: { units: 2, nano: 0 },
  kshort: { units: 2, nano: 0 },
  dlong: { units: 0, nano: 250000000 },
  dshort: { units: 0, nano: ********* },
  dlongMin: { units: 0, nano: ********* },
  dshortMin: { units: 0, nano: 350000000 },
  shortEnabledFlag: true,
  name: 'Локальные валютные облигации',
  exchange: 'spb_etf_t',
  fixedCommission: { units: 1, nano: 500000000 },
  focusType: 'fixed_income',
  releasedDate: 2023-10-19T00:00:00.000Z,
  numShares: undefined,
  countryOfRisk: 'RU',
  countryOfRiskName: 'Российская Федерация',
  sector: 'other',
  rebalancingFreq: 'semi_annual',
  tradingStatus: 5,
  otcFlag: false,
  buyAvailableFlag: true,
  sellAvailableFlag: true,
  minPriceIncrement: { units: 0, nano: 10000000 },
  apiTradeAvailableFlag: true,
  uid: '2f243f46-34ce-4d50-a931-c6f8a67eb758',
  realExchange: 2,
  positionUid: '5b56d3c6-c302-4417-8c0c-a7e8e5881617',
  assetUid: '9b267b9e-d257-4b01-ad84-aac93de0b3f2',
  instrumentExchange: 0,
  forIisFlag: true,
  forQualInvestorFlag: false,
  weekendFlag: false,
  blockedTcaFlag: false,
  liquidityFlag: true,
  first1minCandleDate: 2023-11-23T07:25:00.000Z,
  first1dayCandleDate: 2023-11-23T07:00:00.000Z,
  brand: {
    logoName: 'TLCB.png',
    logoBaseColor: '#000000',
    textColor: '#ffffff'
  },
  dlongClient: { units: 0, nano: ********* },
  dshortClient: { units: 0, nano: 350000000 }
}
2025-08-26T13:24:38.889Z bot:balancer TCS20A107597
2025-08-26T13:24:38.889Z bot:balancer 1
2025-08-26T13:24:38.889Z bot:provider Getting last price
2025-08-26T13:24:39.241Z bot:provider lastPriceResult {
  lastPrices: [
    {
      figi: 'TCS20A107597',
      price: [Object],
      time: 2025-08-26T13:24:30.681Z,
      instrumentUid: '2f243f46-34ce-4d50-a931-c6f8a67eb758',
      lastPriceType: 1
    }
  ]
}
2025-08-26T13:24:39.241Z bot:provider lastPrice { units: 10, nano: 140000000 }
2025-08-26T13:24:44.243Z bot:balancer newPosition {
  pair: 'TLCB/RUB',
  base: 'TLCB',
  quote: 'RUB',
  figi: 'TCS20A107597',
  price: { units: 10, nano: 140000000 },
  priceNumber: 10.14,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 10, nano: 140000000 }
}
2025-08-26T13:24:44.244Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:24:44.244Z bot:balancer positionIndex -1
2025-08-26T13:24:44.244Z bot:balancer Ticker from DesireWallet not found in portfolio. Creating.
2025-08-26T13:24:44.245Z bot:balancer {
  figi: 'TCS70A106DL2',
  ticker: 'TMON@',
  classCode: 'SPBRU',
  isin: 'RU000A106DL2',
  lot: 1,
  currency: 'rub',
  klong: { units: 2, nano: 0 },
  kshort: { units: 2, nano: 0 },
  dlong: { units: 0, nano: 66600000 },
  dshort: undefined,
  dlongMin: { units: 0, nano: 50000000 },
  dshortMin: undefined,
  shortEnabledFlag: false,
  name: 'Денежный рынок',
  exchange: 'spb_etf_ru',
  fixedCommission: { units: 1, nano: 0 },
  focusType: 'equity',
  releasedDate: 2022-12-22T00:00:00.000Z,
  numShares: undefined,
  countryOfRisk: 'RU',
  countryOfRiskName: 'Российская Федерация',
  sector: 'other',
  rebalancingFreq: '',
  tradingStatus: 5,
  otcFlag: false,
  buyAvailableFlag: true,
  sellAvailableFlag: true,
  minPriceIncrement: { units: 0, nano: 10000000 },
  apiTradeAvailableFlag: true,
  uid: '498ec3ff-ef27-4729-9703-a5aac48d5789',
  realExchange: 2,
  positionUid: '96a4604c-79d9-4a30-8a46-95a80dfd9f02',
  assetUid: 'e16bc6c8-63c2-4023-8c95-e0d212aac4b8',
  instrumentExchange: 0,
  forIisFlag: true,
  forQualInvestorFlag: false,
  weekendFlag: false,
  blockedTcaFlag: false,
  liquidityFlag: true,
  first1minCandleDate: 2025-02-25T07:00:00.000Z,
  first1dayCandleDate: 2025-02-25T00:00:00.000Z,
  brand: {
    logoName: 'TMON2.png',
    logoBaseColor: '#000000',
    textColor: '#ffffff'
  },
  dlongClient: { units: 0, nano: 50000000 },
  dshortClient: { units: 0, nano: 0 }
}
2025-08-26T13:24:44.245Z bot:balancer TCS70A106DL2
2025-08-26T13:24:44.245Z bot:balancer 1
2025-08-26T13:24:44.245Z bot:provider Getting last price
2025-08-26T13:24:44.465Z bot:provider lastPriceResult {
  lastPrices: [
    {
      figi: 'TCS70A106DL2',
      price: [Object],
      time: 2025-08-26T13:24:41.508Z,
      instrumentUid: '498ec3ff-ef27-4729-9703-a5aac48d5789',
      lastPriceType: 1
    }
  ]
}
2025-08-26T13:24:44.465Z bot:provider lastPrice { units: 141, nano: 710000000 }
2025-08-26T13:24:49.466Z bot:balancer newPosition {
  pair: 'TMON/RUB',
  base: 'TMON',
  quote: 'RUB',
  figi: 'TCS70A106DL2',
  price: { units: 141, nano: 710000000 },
  priceNumber: 141.71,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 141, nano: 710000000 }
}
2025-08-26T13:24:49.466Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:24:49.466Z bot:balancer positionIndex 4
2025-08-26T13:24:49.466Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:24:49.466Z bot:balancer positionIndex -1
2025-08-26T13:24:49.466Z bot:balancer Ticker from DesireWallet not found in portfolio. Creating.
2025-08-26T13:24:49.468Z bot:balancer {
  figi: 'TCS70A10A1L8',
  ticker: 'TOFZ@',
  classCode: 'SPBRU',
  isin: 'RU000A10A1L8',
  lot: 1,
  currency: 'rub',
  klong: { units: 2, nano: 0 },
  kshort: { units: 2, nano: 0 },
  dlong: { units: 0, nano: 250000000 },
  dshort: { units: 0, nano: ********* },
  dlongMin: { units: 0, nano: ********* },
  dshortMin: { units: 0, nano: 250000000 },
  shortEnabledFlag: true,
  name: 'Государственные облигации',
  exchange: 'spb_etf_ru_noweekend',
  fixedCommission: { units: 1, nano: 500000000 },
  focusType: 'fixed_income',
  releasedDate: 2024-11-07T00:00:00.000Z,
  numShares: undefined,
  countryOfRisk: 'RU',
  countryOfRiskName: 'Российская Федерация',
  sector: 'other',
  rebalancingFreq: 'semi_annual',
  tradingStatus: 5,
  otcFlag: false,
  buyAvailableFlag: true,
  sellAvailableFlag: true,
  minPriceIncrement: { units: 0, nano: 10000000 },
  apiTradeAvailableFlag: true,
  uid: 'c5049184-ded4-49d0-8e14-bffefc40a223',
  realExchange: 2,
  positionUid: '83fd264c-28fc-4b6a-b8f2-ae090148050d',
  assetUid: 'a20a8e51-dd4e-4ba3-beb0-a9bf4dd0a983',
  instrumentExchange: 0,
  forIisFlag: true,
  forQualInvestorFlag: false,
  weekendFlag: false,
  blockedTcaFlag: false,
  liquidityFlag: true,
  first1minCandleDate: 2024-12-09T07:02:00.000Z,
  first1dayCandleDate: 2024-12-09T00:00:00.000Z,
  brand: {
    logoName: 'TOFZ.png',
    logoBaseColor: '#000000',
    textColor: '#ffffff'
  },
  dlongClient: { units: 0, nano: ********* },
  dshortClient: { units: 0, nano: 250000000 }
}
2025-08-26T13:24:49.468Z bot:balancer TCS70A10A1L8
2025-08-26T13:24:49.468Z bot:balancer 1
2025-08-26T13:24:49.468Z bot:provider Getting last price
2025-08-26T13:24:49.695Z bot:provider lastPriceResult {
  lastPrices: [
    {
      figi: 'TCS70A10A1L8',
      price: [Object],
      time: 2025-08-26T13:23:56.140Z,
      instrumentUid: 'c5049184-ded4-49d0-8e14-bffefc40a223',
      lastPriceType: 1
    }
  ]
}
2025-08-26T13:24:49.696Z bot:provider lastPrice { units: 13, nano: 280000000 }
2025-08-26T13:24:54.698Z bot:balancer newPosition {
  pair: 'TOFZ/RUB',
  base: 'TOFZ',
  quote: 'RUB',
  figi: 'TCS70A10A1L8',
  price: { units: 13, nano: 280000000 },
  priceNumber: 13.28,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 13, nano: 280000000 }
}
2025-08-26T13:24:54.699Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:24:54.699Z bot:balancer positionIndex 0
2025-08-26T13:24:54.699Z bot:balancer Calculating totalPrice
2025-08-26T13:24:54.699Z bot:balancer walletWithtotalPrice: map start: position {
  pair: 'RUB/RUB',
  base: 'RUB',
  quote: 'RUB',
  figi: undefined,
  amount: -367,
  lotSize: 1,
  price: { units: 1, nano: 0 },
  priceNumber: 1,
  lotPrice: { units: 1, nano: 0 }
}
2025-08-26T13:24:54.700Z bot:balancer lotPriceNumber 1
2025-08-26T13:24:54.700Z bot:balancer position.amount, position.priceNumber
2025-08-26T13:24:54.700Z bot:balancer -367 1
2025-08-26T13:24:54.700Z bot:balancer totalPriceNumber -367
2025-08-26T13:24:54.700Z bot:balancer totalPrice { units: -367, nano: 0 }
2025-08-26T13:24:54.700Z bot:balancer walletWithtotalPrice: map end: position {
  pair: 'RUB/RUB',
  base: 'RUB',
  quote: 'RUB',
  figi: undefined,
  amount: -367,
  lotSize: 1,
  price: { units: 1, nano: 0 },
  priceNumber: 1,
  lotPrice: { units: 1, nano: 0 },
  totalPrice: { units: -367, nano: 0 }
}
2025-08-26T13:24:54.700Z bot:balancer walletWithtotalPrice: map start: position {
  pair: 'TDIV@/RUB',
  base: 'TDIV@',
  quote: 'RUB',
  figi: 'TCS10A107563',
  amount: 12,
  lotSize: 1,
  price: { units: 10, nano: ********* },
  priceNumber: 10.54,
  lotPrice: { units: 10, nano: ********* },
  totalPrice: { units: 126, nano: ********* },
  totalPriceNumber: 126.47999999999999
}
2025-08-26T13:24:54.700Z bot:balancer lotPriceNumber 10.54
2025-08-26T13:24:54.700Z bot:balancer position.amount, position.priceNumber
2025-08-26T13:24:54.700Z bot:balancer 12 10.54
2025-08-26T13:24:54.700Z bot:balancer totalPriceNumber 126.47999999999999
2025-08-26T13:24:54.700Z bot:balancer totalPrice { units: 126, nano: ********* }
2025-08-26T13:24:54.700Z bot:balancer walletWithtotalPrice: map end: position {
  pair: 'TDIV@/RUB',
  base: 'TDIV@',
  quote: 'RUB',
  figi: 'TCS10A107563',
  amount: 12,
  lotSize: 1,
  price: { units: 10, nano: ********* },
  priceNumber: 10.54,
  lotPrice: { units: 10, nano: ********* },
  totalPrice: { units: 126, nano: ********* },
  totalPriceNumber: 126.47999999999999
}
2025-08-26T13:24:54.700Z bot:balancer walletWithtotalPrice: map start: position {
  pair: 'TGLD@/RUB',
  base: 'TGLD@',
  quote: 'RUB',
  figi: 'TCS80A101X50',
  amount: 12,
  lotSize: 1,
  price: { units: 10, nano: ********* },
  priceNumber: 10.8,
  lotPrice: { units: 10, nano: ********* },
  totalPrice: { units: 129, nano: ********* },
  totalPriceNumber: 129.*********00002
}
2025-08-26T13:24:54.701Z bot:balancer lotPriceNumber 10.8
2025-08-26T13:24:54.701Z bot:balancer position.amount, position.priceNumber
2025-08-26T13:24:54.701Z bot:balancer 12 10.8
2025-08-26T13:24:54.701Z bot:balancer totalPriceNumber 129.*********00002
2025-08-26T13:24:54.701Z bot:balancer totalPrice { units: 129, nano: ********* }
2025-08-26T13:24:54.701Z bot:balancer walletWithtotalPrice: map end: position {
  pair: 'TGLD@/RUB',
  base: 'TGLD@',
  quote: 'RUB',
  figi: 'TCS80A101X50',
  amount: 12,
  lotSize: 1,
  price: { units: 10, nano: ********* },
  priceNumber: 10.8,
  lotPrice: { units: 10, nano: ********* },
  totalPrice: { units: 129, nano: ********* },
  totalPriceNumber: 129.*********00002
}
2025-08-26T13:24:54.701Z bot:balancer walletWithtotalPrice: map start: position {
  pair: 'TRUR/RUB',
  base: 'TRUR',
  quote: 'RUB',
  figi: 'BBG000000001',
  amount: 14,
  lotSize: 1,
  price: { units: 9, nano: ********* },
  priceNumber: 9.3,
  lotPrice: { units: 9, nano: ********* },
  totalPrice: { units: 130, nano: ********* },
  totalPriceNumber: 130.*********00002
}
2025-08-26T13:24:54.701Z bot:balancer lotPriceNumber 9.3
2025-08-26T13:24:54.701Z bot:balancer position.amount, position.priceNumber
2025-08-26T13:24:54.701Z bot:balancer 14 9.3
2025-08-26T13:24:54.701Z bot:balancer totalPriceNumber 130.*********00002
2025-08-26T13:24:54.701Z bot:balancer totalPrice { units: 130, nano: ********* }
2025-08-26T13:24:54.701Z bot:balancer walletWithtotalPrice: map end: position {
  pair: 'TRUR/RUB',
  base: 'TRUR',
  quote: 'RUB',
  figi: 'BBG000000001',
  amount: 14,
  lotSize: 1,
  price: { units: 9, nano: ********* },
  priceNumber: 9.3,
  lotPrice: { units: 9, nano: ********* },
  totalPrice: { units: 130, nano: ********* },
  totalPriceNumber: 130.*********00002
}
2025-08-26T13:24:54.701Z bot:balancer walletWithtotalPrice: map start: position {
  pair: 'TMOS@/RUB',
  base: 'TMOS@',
  quote: 'RUB',
  figi: 'TCS60A101X76',
  amount: 19,
  lotSize: 1,
  price: { units: 6, nano: ********* },
  priceNumber: 6.69,
  lotPrice: { units: 6, nano: ********* },
  totalPrice: { units: 127, nano: ********* },
  totalPriceNumber: 127.*********00001
}
2025-08-26T13:24:54.701Z bot:balancer lotPriceNumber 6.69
2025-08-26T13:24:54.701Z bot:balancer position.amount, position.priceNumber
2025-08-26T13:24:54.701Z bot:balancer 19 6.69
2025-08-26T13:24:54.702Z bot:balancer totalPriceNumber 127.*********00001
2025-08-26T13:24:54.702Z bot:balancer totalPrice { units: 127, nano: ********* }
2025-08-26T13:24:54.702Z bot:balancer walletWithtotalPrice: map end: position {
  pair: 'TMOS@/RUB',
  base: 'TMOS@',
  quote: 'RUB',
  figi: 'TCS60A101X76',
  amount: 19,
  lotSize: 1,
  price: { units: 6, nano: ********* },
  priceNumber: 6.69,
  lotPrice: { units: 6, nano: ********* },
  totalPrice: { units: 127, nano: ********* },
  totalPriceNumber: 127.*********00001
}
2025-08-26T13:24:54.702Z bot:balancer walletWithtotalPrice: map start: position {
  pair: 'TRND/RUB',
  base: 'TRND',
  quote: 'RUB',
  figi: 'TCS00A10B0G9',
  amount: 24,
  lotSize: 1,
  price: { units: 9, nano: ********* },
  priceNumber: 9.88,
  lotPrice: { units: 9, nano: ********* },
  totalPrice: { units: 237, nano: ********* },
  totalPriceNumber: 237.12
}
2025-08-26T13:24:54.702Z bot:balancer lotPriceNumber 9.88
2025-08-26T13:24:54.702Z bot:balancer position.amount, position.priceNumber
2025-08-26T13:24:54.702Z bot:balancer 24 9.88
2025-08-26T13:24:54.702Z bot:balancer totalPriceNumber 237.12
2025-08-26T13:24:54.702Z bot:balancer totalPrice { units: 237, nano: ********* }
2025-08-26T13:24:54.702Z bot:balancer walletWithtotalPrice: map end: position {
  pair: 'TRND/RUB',
  base: 'TRND',
  quote: 'RUB',
  figi: 'TCS00A10B0G9',
  amount: 24,
  lotSize: 1,
  price: { units: 9, nano: ********* },
  priceNumber: 9.88,
  lotPrice: { units: 9, nano: ********* },
  totalPrice: { units: 237, nano: ********* },
  totalPriceNumber: 237.12
}
2025-08-26T13:24:54.702Z bot:balancer walletWithtotalPrice: map start: position {
  pair: 'TPAY/RUB',
  base: 'TPAY',
  quote: 'RUB',
  figi: 'TCS00A108WX3',
  price: { units: 101, nano: ********* },
  priceNumber: 101.69,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 101, nano: ********* }
}
2025-08-26T13:24:54.702Z bot:balancer lotPriceNumber 101.69
2025-08-26T13:24:54.702Z bot:balancer position.amount, position.priceNumber
2025-08-26T13:24:54.702Z bot:balancer 0 101.69
2025-08-26T13:24:54.702Z bot:balancer walletWithtotalPrice: map end: position {
  pair: 'TPAY/RUB',
  base: 'TPAY',
  quote: 'RUB',
  figi: 'TCS00A108WX3',
  price: { units: 101, nano: ********* },
  priceNumber: 101.69,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 101, nano: ********* }
}
2025-08-26T13:24:54.702Z bot:balancer walletWithtotalPrice: map start: position {
  pair: 'TBRU/RUB',
  base: 'TBRU',
  quote: 'RUB',
  figi: 'TCS60A1039N1',
  price: { units: 7, nano: 390000000 },
  priceNumber: 7.39,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 7, nano: 390000000 }
}
2025-08-26T13:24:54.702Z bot:balancer lotPriceNumber 7.39
2025-08-26T13:24:54.702Z bot:balancer position.amount, position.priceNumber
2025-08-26T13:24:54.702Z bot:balancer 0 7.39
2025-08-26T13:24:54.702Z bot:balancer walletWithtotalPrice: map end: position {
  pair: 'TBRU/RUB',
  base: 'TBRU',
  quote: 'RUB',
  figi: 'TCS60A1039N1',
  price: { units: 7, nano: 390000000 },
  priceNumber: 7.39,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 7, nano: 390000000 }
}
2025-08-26T13:24:54.702Z bot:balancer walletWithtotalPrice: map start: position {
  pair: 'TITR/RUB',
  base: 'TITR',
  quote: 'RUB',
  figi: 'TCS30A108BL2',
  price: { units: 6, nano: 660000000 },
  priceNumber: 6.66,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 6, nano: 660000000 }
}
2025-08-26T13:24:54.702Z bot:balancer lotPriceNumber 6.66
2025-08-26T13:24:54.702Z bot:balancer position.amount, position.priceNumber
2025-08-26T13:24:54.702Z bot:balancer 0 6.66
2025-08-26T13:24:54.702Z bot:balancer walletWithtotalPrice: map end: position {
  pair: 'TITR/RUB',
  base: 'TITR',
  quote: 'RUB',
  figi: 'TCS30A108BL2',
  price: { units: 6, nano: 660000000 },
  priceNumber: 6.66,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 6, nano: 660000000 }
}
2025-08-26T13:24:54.702Z bot:balancer walletWithtotalPrice: map start: position {
  pair: 'TLCB/RUB',
  base: 'TLCB',
  quote: 'RUB',
  figi: 'TCS20A107597',
  price: { units: 10, nano: 140000000 },
  priceNumber: 10.14,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 10, nano: 140000000 }
}
2025-08-26T13:24:54.703Z bot:balancer lotPriceNumber 10.14
2025-08-26T13:24:54.703Z bot:balancer position.amount, position.priceNumber
2025-08-26T13:24:54.703Z bot:balancer 0 10.14
2025-08-26T13:24:54.703Z bot:balancer walletWithtotalPrice: map end: position {
  pair: 'TLCB/RUB',
  base: 'TLCB',
  quote: 'RUB',
  figi: 'TCS20A107597',
  price: { units: 10, nano: 140000000 },
  priceNumber: 10.14,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 10, nano: 140000000 }
}
2025-08-26T13:24:54.703Z bot:balancer walletWithtotalPrice: map start: position {
  pair: 'TMON/RUB',
  base: 'TMON',
  quote: 'RUB',
  figi: 'TCS70A106DL2',
  price: { units: 141, nano: 710000000 },
  priceNumber: 141.71,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 141, nano: 710000000 }
}
2025-08-26T13:24:54.703Z bot:balancer lotPriceNumber 141.71
2025-08-26T13:24:54.703Z bot:balancer position.amount, position.priceNumber
2025-08-26T13:24:54.703Z bot:balancer 0 141.71
2025-08-26T13:24:54.703Z bot:balancer walletWithtotalPrice: map end: position {
  pair: 'TMON/RUB',
  base: 'TMON',
  quote: 'RUB',
  figi: 'TCS70A106DL2',
  price: { units: 141, nano: 710000000 },
  priceNumber: 141.71,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 141, nano: 710000000 }
}
2025-08-26T13:24:54.703Z bot:balancer walletWithtotalPrice: map start: position {
  pair: 'TOFZ/RUB',
  base: 'TOFZ',
  quote: 'RUB',
  figi: 'TCS70A10A1L8',
  price: { units: 13, nano: 280000000 },
  priceNumber: 13.28,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 13, nano: 280000000 }
}
2025-08-26T13:24:54.703Z bot:balancer lotPriceNumber 13.28
2025-08-26T13:24:54.703Z bot:balancer position.amount, position.priceNumber
2025-08-26T13:24:54.703Z bot:balancer 0 13.28
2025-08-26T13:24:54.703Z bot:balancer walletWithtotalPrice: map end: position {
  pair: 'TOFZ/RUB',
  base: 'TOFZ',
  quote: 'RUB',
  figi: 'TCS70A10A1L8',
  price: { units: 13, nano: 280000000 },
  priceNumber: 13.28,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 13, nano: 280000000 }
}
2025-08-26T13:24:54.703Z bot:balancer addNumbersToPosition start
2025-08-26T13:24:54.703Z bot:balancer position.price { units: 1, nano: 0 }
2025-08-26T13:24:54.703Z bot:balancer position.priceNumber 1
2025-08-26T13:24:54.703Z bot:balancer position.lotPrice { units: 1, nano: 0 }
2025-08-26T13:24:54.703Z bot:balancer position.lotPriceNumber 1
2025-08-26T13:24:54.703Z bot:balancer position.totalPrice { units: -367, nano: 0 }
2025-08-26T13:24:54.703Z bot:balancer position.totalPriceNumber -367
2025-08-26T13:24:54.703Z bot:balancer addNumbersToPosition end {
  pair: 'RUB/RUB',
  base: 'RUB',
  quote: 'RUB',
  figi: undefined,
  amount: -367,
  lotSize: 1,
  price: { units: 1, nano: 0 },
  priceNumber: 1,
  lotPrice: { units: 1, nano: 0 },
  totalPrice: { units: -367, nano: 0 },
  lotPriceNumber: 1,
  totalPriceNumber: -367
}
2025-08-26T13:24:54.704Z bot:balancer addNumbersToPosition start
2025-08-26T13:24:54.704Z bot:balancer position.price { units: 10, nano: ********* }
2025-08-26T13:24:54.704Z bot:balancer position.priceNumber 10.54
2025-08-26T13:24:54.704Z bot:balancer position.lotPrice { units: 10, nano: ********* }
2025-08-26T13:24:54.704Z bot:balancer position.lotPriceNumber 10.54
2025-08-26T13:24:54.704Z bot:balancer position.totalPrice { units: 126, nano: ********* }
2025-08-26T13:24:54.704Z bot:balancer position.totalPriceNumber 126.48
2025-08-26T13:24:54.704Z bot:balancer addNumbersToPosition end {
  pair: 'TDIV@/RUB',
  base: 'TDIV@',
  quote: 'RUB',
  figi: 'TCS10A107563',
  amount: 12,
  lotSize: 1,
  price: { units: 10, nano: ********* },
  priceNumber: 10.54,
  lotPrice: { units: 10, nano: ********* },
  totalPrice: { units: 126, nano: ********* },
  totalPriceNumber: 126.48,
  lotPriceNumber: 10.54
}
2025-08-26T13:24:54.704Z bot:balancer addNumbersToPosition start
2025-08-26T13:24:54.704Z bot:balancer position.price { units: 10, nano: ********* }
2025-08-26T13:24:54.704Z bot:balancer position.priceNumber 10.8
2025-08-26T13:24:54.704Z bot:balancer position.lotPrice { units: 10, nano: ********* }
2025-08-26T13:24:54.704Z bot:balancer position.lotPriceNumber 10.8
2025-08-26T13:24:54.704Z bot:balancer position.totalPrice { units: 129, nano: ********* }
2025-08-26T13:24:54.704Z bot:balancer position.totalPriceNumber 129.6
2025-08-26T13:24:54.704Z bot:balancer addNumbersToPosition end {
  pair: 'TGLD@/RUB',
  base: 'TGLD@',
  quote: 'RUB',
  figi: 'TCS80A101X50',
  amount: 12,
  lotSize: 1,
  price: { units: 10, nano: ********* },
  priceNumber: 10.8,
  lotPrice: { units: 10, nano: ********* },
  totalPrice: { units: 129, nano: ********* },
  totalPriceNumber: 129.6,
  lotPriceNumber: 10.8
}
2025-08-26T13:24:54.704Z bot:balancer addNumbersToPosition start
2025-08-26T13:24:54.704Z bot:balancer position.price { units: 9, nano: ********* }
2025-08-26T13:24:54.704Z bot:balancer position.priceNumber 9.3
2025-08-26T13:24:54.704Z bot:balancer position.lotPrice { units: 9, nano: ********* }
2025-08-26T13:24:54.704Z bot:balancer position.lotPriceNumber 9.3
2025-08-26T13:24:54.704Z bot:balancer position.totalPrice { units: 130, nano: ********* }
2025-08-26T13:24:54.704Z bot:balancer position.totalPriceNumber 130.2
2025-08-26T13:24:54.704Z bot:balancer addNumbersToPosition end {
  pair: 'TRUR/RUB',
  base: 'TRUR',
  quote: 'RUB',
  figi: 'BBG000000001',
  amount: 14,
  lotSize: 1,
  price: { units: 9, nano: ********* },
  priceNumber: 9.3,
  lotPrice: { units: 9, nano: ********* },
  totalPrice: { units: 130, nano: ********* },
  totalPriceNumber: 130.2,
  lotPriceNumber: 9.3
}
2025-08-26T13:24:54.704Z bot:balancer addNumbersToPosition start
2025-08-26T13:24:54.704Z bot:balancer position.price { units: 6, nano: ********* }
2025-08-26T13:24:54.704Z bot:balancer position.priceNumber 6.69
2025-08-26T13:24:54.704Z bot:balancer position.lotPrice { units: 6, nano: ********* }
2025-08-26T13:24:54.704Z bot:balancer position.lotPriceNumber 6.69
2025-08-26T13:24:54.704Z bot:balancer position.totalPrice { units: 127, nano: ********* }
2025-08-26T13:24:54.704Z bot:balancer position.totalPriceNumber 127.11
2025-08-26T13:24:54.704Z bot:balancer addNumbersToPosition end {
  pair: 'TMOS@/RUB',
  base: 'TMOS@',
  quote: 'RUB',
  figi: 'TCS60A101X76',
  amount: 19,
  lotSize: 1,
  price: { units: 6, nano: ********* },
  priceNumber: 6.69,
  lotPrice: { units: 6, nano: ********* },
  totalPrice: { units: 127, nano: ********* },
  totalPriceNumber: 127.11,
  lotPriceNumber: 6.69
}
2025-08-26T13:24:54.705Z bot:balancer addNumbersToPosition start
2025-08-26T13:24:54.705Z bot:balancer position.price { units: 9, nano: ********* }
2025-08-26T13:24:54.705Z bot:balancer position.priceNumber 9.88
2025-08-26T13:24:54.705Z bot:balancer position.lotPrice { units: 9, nano: ********* }
2025-08-26T13:24:54.705Z bot:balancer position.lotPriceNumber 9.88
2025-08-26T13:24:54.705Z bot:balancer position.totalPrice { units: 237, nano: ********* }
2025-08-26T13:24:54.705Z bot:balancer position.totalPriceNumber 237.12
2025-08-26T13:24:54.705Z bot:balancer addNumbersToPosition end {
  pair: 'TRND/RUB',
  base: 'TRND',
  quote: 'RUB',
  figi: 'TCS00A10B0G9',
  amount: 24,
  lotSize: 1,
  price: { units: 9, nano: ********* },
  priceNumber: 9.88,
  lotPrice: { units: 9, nano: ********* },
  totalPrice: { units: 237, nano: ********* },
  totalPriceNumber: 237.12,
  lotPriceNumber: 9.88
}
2025-08-26T13:24:54.705Z bot:balancer addNumbersToPosition start
2025-08-26T13:24:54.705Z bot:balancer position.price { units: 101, nano: ********* }
2025-08-26T13:24:54.705Z bot:balancer position.priceNumber 101.69
2025-08-26T13:24:54.705Z bot:balancer position.lotPrice { units: 101, nano: ********* }
2025-08-26T13:24:54.705Z bot:balancer position.lotPriceNumber 101.69
2025-08-26T13:24:54.705Z bot:balancer position.totalPrice undefined
2025-08-26T13:24:54.705Z bot:balancer addNumbersToPosition end {
  pair: 'TPAY/RUB',
  base: 'TPAY',
  quote: 'RUB',
  figi: 'TCS00A108WX3',
  price: { units: 101, nano: ********* },
  priceNumber: 101.69,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 101, nano: ********* },
  lotPriceNumber: 101.69
}
2025-08-26T13:24:54.705Z bot:balancer addNumbersToPosition start
2025-08-26T13:24:54.705Z bot:balancer position.price { units: 7, nano: 390000000 }
2025-08-26T13:24:54.705Z bot:balancer position.priceNumber 7.39
2025-08-26T13:24:54.705Z bot:balancer position.lotPrice { units: 7, nano: 390000000 }
2025-08-26T13:24:54.705Z bot:balancer position.lotPriceNumber 7.39
2025-08-26T13:24:54.705Z bot:balancer position.totalPrice undefined
2025-08-26T13:24:54.705Z bot:balancer addNumbersToPosition end {
  pair: 'TBRU/RUB',
  base: 'TBRU',
  quote: 'RUB',
  figi: 'TCS60A1039N1',
  price: { units: 7, nano: 390000000 },
  priceNumber: 7.39,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 7, nano: 390000000 },
  lotPriceNumber: 7.39
}
2025-08-26T13:24:54.705Z bot:balancer addNumbersToPosition start
2025-08-26T13:24:54.705Z bot:balancer position.price { units: 6, nano: 660000000 }
2025-08-26T13:24:54.705Z bot:balancer position.priceNumber 6.66
2025-08-26T13:24:54.705Z bot:balancer position.lotPrice { units: 6, nano: 660000000 }
2025-08-26T13:24:54.705Z bot:balancer position.lotPriceNumber 6.66
2025-08-26T13:24:54.705Z bot:balancer position.totalPrice undefined
2025-08-26T13:24:54.705Z bot:balancer addNumbersToPosition end {
  pair: 'TITR/RUB',
  base: 'TITR',
  quote: 'RUB',
  figi: 'TCS30A108BL2',
  price: { units: 6, nano: 660000000 },
  priceNumber: 6.66,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 6, nano: 660000000 },
  lotPriceNumber: 6.66
}
2025-08-26T13:24:54.705Z bot:balancer addNumbersToPosition start
2025-08-26T13:24:54.705Z bot:balancer position.price { units: 10, nano: 140000000 }
2025-08-26T13:24:54.705Z bot:balancer position.priceNumber 10.14
2025-08-26T13:24:54.705Z bot:balancer position.lotPrice { units: 10, nano: 140000000 }
2025-08-26T13:24:54.705Z bot:balancer position.lotPriceNumber 10.14
2025-08-26T13:24:54.705Z bot:balancer position.totalPrice undefined
2025-08-26T13:24:54.705Z bot:balancer addNumbersToPosition end {
  pair: 'TLCB/RUB',
  base: 'TLCB',
  quote: 'RUB',
  figi: 'TCS20A107597',
  price: { units: 10, nano: 140000000 },
  priceNumber: 10.14,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 10, nano: 140000000 },
  lotPriceNumber: 10.14
}
2025-08-26T13:24:54.705Z bot:balancer addNumbersToPosition start
2025-08-26T13:24:54.705Z bot:balancer position.price { units: 141, nano: 710000000 }
2025-08-26T13:24:54.705Z bot:balancer position.priceNumber 141.71
2025-08-26T13:24:54.705Z bot:balancer position.lotPrice { units: 141, nano: 710000000 }
2025-08-26T13:24:54.705Z bot:balancer position.lotPriceNumber 141.71
2025-08-26T13:24:54.705Z bot:balancer position.totalPrice undefined
2025-08-26T13:24:54.705Z bot:balancer addNumbersToPosition end {
  pair: 'TMON/RUB',
  base: 'TMON',
  quote: 'RUB',
  figi: 'TCS70A106DL2',
  price: { units: 141, nano: 710000000 },
  priceNumber: 141.71,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 141, nano: 710000000 },
  lotPriceNumber: 141.71
}
2025-08-26T13:24:54.706Z bot:balancer addNumbersToPosition start
2025-08-26T13:24:54.706Z bot:balancer position.price { units: 13, nano: 280000000 }
2025-08-26T13:24:54.706Z bot:balancer position.priceNumber 13.28
2025-08-26T13:24:54.706Z bot:balancer position.lotPrice { units: 13, nano: 280000000 }
2025-08-26T13:24:54.706Z bot:balancer position.lotPriceNumber 13.28
2025-08-26T13:24:54.706Z bot:balancer position.totalPrice undefined
2025-08-26T13:24:54.706Z bot:balancer addNumbersToPosition end {
  pair: 'TOFZ/RUB',
  base: 'TOFZ',
  quote: 'RUB',
  figi: 'TCS70A10A1L8',
  price: { units: 13, nano: 280000000 },
  priceNumber: 13.28,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 13, nano: 280000000 },
  lotPriceNumber: 13.28
}
2025-08-26T13:24:54.706Z bot:balancer addNumbersToWallet [
  {
    pair: 'RUB/RUB',
    base: 'RUB',
    quote: 'RUB',
    figi: undefined,
    amount: -367,
    lotSize: 1,
    price: { units: 1, nano: 0 },
    priceNumber: 1,
    lotPrice: { units: 1, nano: 0 },
    totalPrice: { units: -367, nano: 0 },
    lotPriceNumber: 1,
    totalPriceNumber: -367
  },
  {
    pair: 'TDIV@/RUB',
    base: 'TDIV@',
    quote: 'RUB',
    figi: 'TCS10A107563',
    amount: 12,
    lotSize: 1,
    price: { units: 10, nano: ********* },
    priceNumber: 10.54,
    lotPrice: { units: 10, nano: ********* },
    totalPrice: { units: 126, nano: ********* },
    totalPriceNumber: 126.48,
    lotPriceNumber: 10.54
  },
  {
    pair: 'TGLD@/RUB',
    base: 'TGLD@',
    quote: 'RUB',
    figi: 'TCS80A101X50',
    amount: 12,
    lotSize: 1,
    price: { units: 10, nano: ********* },
    priceNumber: 10.8,
    lotPrice: { units: 10, nano: ********* },
    totalPrice: { units: 129, nano: ********* },
    totalPriceNumber: 129.6,
    lotPriceNumber: 10.8
  },
  {
    pair: 'TRUR/RUB',
    base: 'TRUR',
    quote: 'RUB',
    figi: 'BBG000000001',
    amount: 14,
    lotSize: 1,
    price: { units: 9, nano: ********* },
    priceNumber: 9.3,
    lotPrice: { units: 9, nano: ********* },
    totalPrice: { units: 130, nano: ********* },
    totalPriceNumber: 130.2,
    lotPriceNumber: 9.3
  },
  {
    pair: 'TMOS@/RUB',
    base: 'TMOS@',
    quote: 'RUB',
    figi: 'TCS60A101X76',
    amount: 19,
    lotSize: 1,
    price: { units: 6, nano: ********* },
    priceNumber: 6.69,
    lotPrice: { units: 6, nano: ********* },
    totalPrice: { units: 127, nano: ********* },
    totalPriceNumber: 127.11,
    lotPriceNumber: 6.69
  },
  {
    pair: 'TRND/RUB',
    base: 'TRND',
    quote: 'RUB',
    figi: 'TCS00A10B0G9',
    amount: 24,
    lotSize: 1,
    price: { units: 9, nano: ********* },
    priceNumber: 9.88,
    lotPrice: { units: 9, nano: ********* },
    totalPrice: { units: 237, nano: ********* },
    totalPriceNumber: 237.12,
    lotPriceNumber: 9.88
  },
  {
    pair: 'TPAY/RUB',
    base: 'TPAY',
    quote: 'RUB',
    figi: 'TCS00A108WX3',
    price: { units: 101, nano: ********* },
    priceNumber: 101.69,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 101, nano: ********* },
    lotPriceNumber: 101.69
  },
  {
    pair: 'TBRU/RUB',
    base: 'TBRU',
    quote: 'RUB',
    figi: 'TCS60A1039N1',
    price: { units: 7, nano: 390000000 },
    priceNumber: 7.39,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 7, nano: 390000000 },
    lotPriceNumber: 7.39
  },
  {
    pair: 'TITR/RUB',
    base: 'TITR',
    quote: 'RUB',
    figi: 'TCS30A108BL2',
    price: { units: 6, nano: 660000000 },
    priceNumber: 6.66,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 6, nano: 660000000 },
    lotPriceNumber: 6.66
  },
  {
    pair: 'TLCB/RUB',
    base: 'TLCB',
    quote: 'RUB',
    figi: 'TCS20A107597',
    price: { units: 10, nano: 140000000 },
    priceNumber: 10.14,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 10, nano: 140000000 },
    lotPriceNumber: 10.14
  },
  {
    pair: 'TMON/RUB',
    base: 'TMON',
    quote: 'RUB',
    figi: 'TCS70A106DL2',
    price: { units: 141, nano: 710000000 },
    priceNumber: 141.71,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 141, nano: 710000000 },
    lotPriceNumber: 141.71
  },
  {
    pair: 'TOFZ/RUB',
    base: 'TOFZ',
    quote: 'RUB',
    figi: 'TCS70A10A1L8',
    price: { units: 13, nano: 280000000 },
    priceNumber: 13.28,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 13, nano: 280000000 },
    lotPriceNumber: 13.28
  }
]
2025-08-26T13:24:54.709Z bot:balancer addNumbersToWallet [Function: addNumbersToWallet]
2025-08-26T13:24:54.712Z bot:balancer sortedWallet [
  {
    pair: 'TMON/RUB',
    base: 'TMON',
    quote: 'RUB',
    figi: 'TCS70A106DL2',
    price: { units: 141, nano: 710000000 },
    priceNumber: 141.71,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 141, nano: 710000000 },
    lotPriceNumber: 141.71
  },
  {
    pair: 'TPAY/RUB',
    base: 'TPAY',
    quote: 'RUB',
    figi: 'TCS00A108WX3',
    price: { units: 101, nano: ********* },
    priceNumber: 101.69,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 101, nano: ********* },
    lotPriceNumber: 101.69
  },
  {
    pair: 'TOFZ/RUB',
    base: 'TOFZ',
    quote: 'RUB',
    figi: 'TCS70A10A1L8',
    price: { units: 13, nano: 280000000 },
    priceNumber: 13.28,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 13, nano: 280000000 },
    lotPriceNumber: 13.28
  },
  {
    pair: 'TGLD@/RUB',
    base: 'TGLD@',
    quote: 'RUB',
    figi: 'TCS80A101X50',
    amount: 12,
    lotSize: 1,
    price: { units: 10, nano: ********* },
    priceNumber: 10.8,
    lotPrice: { units: 10, nano: ********* },
    totalPrice: { units: 129, nano: ********* },
    totalPriceNumber: 129.6,
    lotPriceNumber: 10.8
  },
  {
    pair: 'TDIV@/RUB',
    base: 'TDIV@',
    quote: 'RUB',
    figi: 'TCS10A107563',
    amount: 12,
    lotSize: 1,
    price: { units: 10, nano: ********* },
    priceNumber: 10.54,
    lotPrice: { units: 10, nano: ********* },
    totalPrice: { units: 126, nano: ********* },
    totalPriceNumber: 126.48,
    lotPriceNumber: 10.54
  },
  {
    pair: 'TLCB/RUB',
    base: 'TLCB',
    quote: 'RUB',
    figi: 'TCS20A107597',
    price: { units: 10, nano: 140000000 },
    priceNumber: 10.14,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 10, nano: 140000000 },
    lotPriceNumber: 10.14
  },
  {
    pair: 'TRND/RUB',
    base: 'TRND',
    quote: 'RUB',
    figi: 'TCS00A10B0G9',
    amount: 24,
    lotSize: 1,
    price: { units: 9, nano: ********* },
    priceNumber: 9.88,
    lotPrice: { units: 9, nano: ********* },
    totalPrice: { units: 237, nano: ********* },
    totalPriceNumber: 237.12,
    lotPriceNumber: 9.88
  },
  {
    pair: 'TRUR/RUB',
    base: 'TRUR',
    quote: 'RUB',
    figi: 'BBG000000001',
    amount: 14,
    lotSize: 1,
    price: { units: 9, nano: ********* },
    priceNumber: 9.3,
    lotPrice: { units: 9, nano: ********* },
    totalPrice: { units: 130, nano: ********* },
    totalPriceNumber: 130.2,
    lotPriceNumber: 9.3
  },
  {
    pair: 'TBRU/RUB',
    base: 'TBRU',
    quote: 'RUB',
    figi: 'TCS60A1039N1',
    price: { units: 7, nano: 390000000 },
    priceNumber: 7.39,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 7, nano: 390000000 },
    lotPriceNumber: 7.39
  },
  {
    pair: 'TMOS@/RUB',
    base: 'TMOS@',
    quote: 'RUB',
    figi: 'TCS60A101X76',
    amount: 19,
    lotSize: 1,
    price: { units: 6, nano: ********* },
    priceNumber: 6.69,
    lotPrice: { units: 6, nano: ********* },
    totalPrice: { units: 127, nano: ********* },
    totalPriceNumber: 127.11,
    lotPriceNumber: 6.69
  },
  {
    pair: 'TITR/RUB',
    base: 'TITR',
    quote: 'RUB',
    figi: 'TCS30A108BL2',
    price: { units: 6, nano: 660000000 },
    priceNumber: 6.66,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 6, nano: 660000000 },
    lotPriceNumber: 6.66
  },
  {
    pair: 'RUB/RUB',
    base: 'RUB',
    quote: 'RUB',
    figi: undefined,
    amount: -367,
    lotSize: 1,
    price: { units: 1, nano: 0 },
    priceNumber: 1,
    lotPrice: { units: 1, nano: 0 },
    totalPrice: { units: -367, nano: 0 },
    lotPriceNumber: 1,
    totalPriceNumber: -367
  }
]
2025-08-26T13:24:54.713Z bot:balancer Summing up all positions in portfolio
2025-08-26T13:24:54.714Z bot:balancer [
  {
    pair: 'TMON/RUB',
    base: 'TMON',
    quote: 'RUB',
    figi: 'TCS70A106DL2',
    price: { units: 141, nano: 710000000 },
    priceNumber: 141.71,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 141, nano: 710000000 },
    lotPriceNumber: 141.71
  },
  {
    pair: 'TPAY/RUB',
    base: 'TPAY',
    quote: 'RUB',
    figi: 'TCS00A108WX3',
    price: { units: 101, nano: ********* },
    priceNumber: 101.69,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 101, nano: ********* },
    lotPriceNumber: 101.69
  },
  {
    pair: 'TOFZ/RUB',
    base: 'TOFZ',
    quote: 'RUB',
    figi: 'TCS70A10A1L8',
    price: { units: 13, nano: 280000000 },
    priceNumber: 13.28,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 13, nano: 280000000 },
    lotPriceNumber: 13.28
  },
  {
    pair: 'TGLD@/RUB',
    base: 'TGLD@',
    quote: 'RUB',
    figi: 'TCS80A101X50',
    amount: 12,
    lotSize: 1,
    price: { units: 10, nano: ********* },
    priceNumber: 10.8,
    lotPrice: { units: 10, nano: ********* },
    totalPrice: { units: 129, nano: ********* },
    totalPriceNumber: 129.6,
    lotPriceNumber: 10.8
  },
  {
    pair: 'TDIV@/RUB',
    base: 'TDIV@',
    quote: 'RUB',
    figi: 'TCS10A107563',
    amount: 12,
    lotSize: 1,
    price: { units: 10, nano: ********* },
    priceNumber: 10.54,
    lotPrice: { units: 10, nano: ********* },
    totalPrice: { units: 126, nano: ********* },
    totalPriceNumber: 126.48,
    lotPriceNumber: 10.54
  },
  {
    pair: 'TLCB/RUB',
    base: 'TLCB',
    quote: 'RUB',
    figi: 'TCS20A107597',
    price: { units: 10, nano: 140000000 },
    priceNumber: 10.14,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 10, nano: 140000000 },
    lotPriceNumber: 10.14
  },
  {
    pair: 'TRND/RUB',
    base: 'TRND',
    quote: 'RUB',
    figi: 'TCS00A10B0G9',
    amount: 24,
    lotSize: 1,
    price: { units: 9, nano: ********* },
    priceNumber: 9.88,
    lotPrice: { units: 9, nano: ********* },
    totalPrice: { units: 237, nano: ********* },
    totalPriceNumber: 237.12,
    lotPriceNumber: 9.88
  },
  {
    pair: 'TRUR/RUB',
    base: 'TRUR',
    quote: 'RUB',
    figi: 'BBG000000001',
    amount: 14,
    lotSize: 1,
    price: { units: 9, nano: ********* },
    priceNumber: 9.3,
    lotPrice: { units: 9, nano: ********* },
    totalPrice: { units: 130, nano: ********* },
    totalPriceNumber: 130.2,
    lotPriceNumber: 9.3
  },
  {
    pair: 'TBRU/RUB',
    base: 'TBRU',
    quote: 'RUB',
    figi: 'TCS60A1039N1',
    price: { units: 7, nano: 390000000 },
    priceNumber: 7.39,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 7, nano: 390000000 },
    lotPriceNumber: 7.39
  },
  {
    pair: 'TMOS@/RUB',
    base: 'TMOS@',
    quote: 'RUB',
    figi: 'TCS60A101X76',
    amount: 19,
    lotSize: 1,
    price: { units: 6, nano: ********* },
    priceNumber: 6.69,
    lotPrice: { units: 6, nano: ********* },
    totalPrice: { units: 127, nano: ********* },
    totalPriceNumber: 127.11,
    lotPriceNumber: 6.69
  },
  {
    pair: 'TITR/RUB',
    base: 'TITR',
    quote: 'RUB',
    figi: 'TCS30A108BL2',
    price: { units: 6, nano: 660000000 },
    priceNumber: 6.66,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 6, nano: 660000000 },
    lotPriceNumber: 6.66
  },
  {
    pair: 'RUB/RUB',
    base: 'RUB',
    quote: 'RUB',
    figi: undefined,
    amount: -367,
    lotSize: 1,
    price: { units: 1, nano: 0 },
    priceNumber: 1,
    lotPrice: { units: 1, nano: 0 },
    totalPrice: { units: -367, nano: 0 },
    lotPriceNumber: 1,
    totalPriceNumber: -367
  }
]
2025-08-26T13:24:54.714Z bot:balancer walletSumNumber 383.51
2025-08-26T13:24:54.715Z bot:balancer Optimal position sizes: {
  TPAY: {
    baseSize: 56.457585878414655,
    marginSize: 169.37275763524397,
    totalSize: 225.83034351365862
  },
  TGLD: {
    baseSize: 11.26186380894447,
    marginSize: 33.78559142683341,
    totalSize: 45.**************
  },
  TRUR: {
    baseSize: 20.92466514070114,
    marginSize: 62.77399542210342,
    totalSize: 83.**************
  },
  TRND: {
    baseSize: 0.2622106783807303,
    marginSize: 0.7866320351421909,
    totalSize: 1.****************
  },
  TBRU: {
    baseSize: 6.243661785024081,
    marginSize: 18.73098535507224,
    totalSize: 24.97464714009632
  },
  TDIV: {
    baseSize: 1.248839544513862,
    marginSize: 3.746518633541586,
    totalSize: 4.***************
  },
  TITR: {
    baseSize: 0.557300810381141,
    marginSize: 1.671902431143423,
    totalSize: 2.229203241524564
  },
  TLCB: {
    baseSize: 13.480214587452153,
    marginSize: 40.44064376235646,
    totalSize: 53.92085834980861
  },
  TMON: {
    baseSize: 253.38494738221402,
    marginSize: 760.154842146642,
    totalSize: 1013.539789528856
  },
  TMOS: {
    baseSize: 15.175838338485164,
    marginSize: 45.52751501545549,
    totalSize: 60.***************
  },
  TOFZ: {
    baseSize: 4.512872045488613,
    marginSize: 13.538616136465837,
    totalSize: 18.05148818195445
  },
  RUB: { baseSize: 0, marginSize: 0, totalSize: 0 }
}
2025-08-26T13:24:54.715Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:24:54.715Z bot:balancer positionIndex 1
2025-08-26T13:24:54.715Z bot:balancer position {
  pair: 'TPAY/RUB',
  base: 'TPAY',
  quote: 'RUB',
  figi: 'TCS00A108WX3',
  price: { units: 101, nano: ********* },
  priceNumber: 101.69,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 101, nano: ********* },
  lotPriceNumber: 101.69
}
2025-08-26T13:24:54.715Z bot:balancer Calculating how many rubles the desired share will be with multiplier
2025-08-26T13:24:54.715Z bot:balancer walletSumNumber 383.51
2025-08-26T13:24:54.715Z bot:balancer desiredPercent 14.721281290817622
2025-08-26T13:24:54.715Z bot:balancer desiredAmountNumber (considering multiplier) 225.83034351365862
2025-08-26T13:24:54.715Z bot:balancer Calculating how many lots can be bought before desired target
2025-08-26T13:24:54.715Z bot:balancer canBuyBeforeTargetLots 2
2025-08-26T13:24:54.715Z bot:balancer Calculating cost of position that can be bought before desired target
2025-08-26T13:24:54.715Z bot:balancer canBuyBeforeTargetNumber 203.38
2025-08-26T13:24:54.715Z bot:balancer Calculating difference between desired value and value before target. Unallocated remainder.
2025-08-26T13:24:54.715Z bot:balancer beforeDiffNumber 22.450343513658623
2025-08-26T13:24:54.715Z bot:balancer Summing up remainders
2025-08-26T13:24:54.715Z bot:balancer How much to buy (can be negative, then need to sell)
2025-08-26T13:24:54.715Z bot:balancer How many lots to buy (can be negative, then need to sell)
2025-08-26T13:24:54.715Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:24:54.715Z bot:balancer positionIndex 3
2025-08-26T13:24:54.715Z bot:balancer position {
  pair: 'TGLD@/RUB',
  base: 'TGLD@',
  quote: 'RUB',
  figi: 'TCS80A101X50',
  amount: 12,
  lotSize: 1,
  price: { units: 10, nano: ********* },
  priceNumber: 10.8,
  lotPrice: { units: 10, nano: ********* },
  totalPrice: { units: 129, nano: ********* },
  totalPriceNumber: 129.6,
  lotPriceNumber: 10.8
}
2025-08-26T13:24:54.715Z bot:balancer Calculating how many rubles the desired share will be with multiplier
2025-08-26T13:24:54.715Z bot:balancer walletSumNumber 383.51
2025-08-26T13:24:54.715Z bot:balancer desiredPercent 2.936524160763597
2025-08-26T13:24:54.715Z bot:balancer desiredAmountNumber (considering multiplier) 45.**************
2025-08-26T13:24:54.715Z bot:balancer Calculating how many lots can be bought before desired target
2025-08-26T13:24:54.715Z bot:balancer canBuyBeforeTargetLots 4
2025-08-26T13:24:54.715Z bot:balancer Calculating cost of position that can be bought before desired target
2025-08-26T13:24:54.715Z bot:balancer canBuyBeforeTargetNumber 43.2
2025-08-26T13:24:54.715Z bot:balancer Calculating difference between desired value and value before target. Unallocated remainder.
2025-08-26T13:24:54.715Z bot:balancer beforeDiffNumber 1.***************
2025-08-26T13:24:54.716Z bot:balancer Summing up remainders
2025-08-26T13:24:54.716Z bot:balancer How much to buy (can be negative, then need to sell)
2025-08-26T13:24:54.716Z bot:balancer toBuyNumber -86.**************
2025-08-26T13:24:54.716Z bot:balancer How many lots to buy (can be negative, then need to sell)
2025-08-26T13:24:54.716Z bot:balancer toBuyLots -8
2025-08-26T13:24:54.716Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:24:54.716Z bot:balancer positionIndex 7
2025-08-26T13:24:54.716Z bot:balancer position {
  pair: 'TRUR/RUB',
  base: 'TRUR',
  quote: 'RUB',
  figi: 'BBG000000001',
  amount: 14,
  lotSize: 1,
  price: { units: 9, nano: ********* },
  priceNumber: 9.3,
  lotPrice: { units: 9, nano: ********* },
  totalPrice: { units: 130, nano: ********* },
  totalPriceNumber: 130.2,
  lotPriceNumber: 9.3
}
2025-08-26T13:24:54.716Z bot:balancer Calculating how many rubles the desired share will be with multiplier
2025-08-26T13:24:54.716Z bot:balancer walletSumNumber 383.51
2025-08-26T13:24:54.716Z bot:balancer desiredPercent 5.456093750019853
2025-08-26T13:24:54.716Z bot:balancer desiredAmountNumber (considering multiplier) 83.**************
2025-08-26T13:24:54.716Z bot:balancer Calculating how many lots can be bought before desired target
2025-08-26T13:24:54.716Z bot:balancer canBuyBeforeTargetLots 8
2025-08-26T13:24:54.716Z bot:balancer Calculating cost of position that can be bought before desired target
2025-08-26T13:24:54.716Z bot:balancer canBuyBeforeTargetNumber 74.4
2025-08-26T13:24:54.716Z bot:balancer Calculating difference between desired value and value before target. Unallocated remainder.
2025-08-26T13:24:54.716Z bot:balancer beforeDiffNumber 9.***************
2025-08-26T13:24:54.716Z bot:balancer Summing up remainders
2025-08-26T13:24:54.716Z bot:balancer How much to buy (can be negative, then need to sell)
2025-08-26T13:24:54.716Z bot:balancer toBuyNumber -55.**************
2025-08-26T13:24:54.716Z bot:balancer How many lots to buy (can be negative, then need to sell)
2025-08-26T13:24:54.716Z bot:balancer toBuyLots -6
2025-08-26T13:24:54.716Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:24:54.716Z bot:balancer positionIndex 6
2025-08-26T13:24:54.716Z bot:balancer position {
  pair: 'TRND/RUB',
  base: 'TRND',
  quote: 'RUB',
  figi: 'TCS00A10B0G9',
  amount: 24,
  lotSize: 1,
  price: { units: 9, nano: ********* },
  priceNumber: 9.88,
  lotPrice: { units: 9, nano: ********* },
  totalPrice: { units: 237, nano: ********* },
  totalPriceNumber: 237.12,
  lotPriceNumber: 9.88
}
2025-08-26T13:24:54.716Z bot:balancer Calculating how many rubles the desired share will be with multiplier
2025-08-26T13:24:54.716Z bot:balancer walletSumNumber 383.51
2025-08-26T13:24:54.716Z bot:balancer desiredPercent 0.06837127542456008
2025-08-26T13:24:54.716Z bot:balancer desiredAmountNumber (considering multiplier) 1.****************
2025-08-26T13:24:54.716Z bot:balancer Calculating how many lots can be bought before desired target
2025-08-26T13:24:54.716Z bot:balancer canBuyBeforeTargetLots 0
2025-08-26T13:24:54.716Z bot:balancer Calculating cost of position that can be bought before desired target
2025-08-26T13:24:54.716Z bot:balancer canBuyBeforeTargetNumber 0
2025-08-26T13:24:54.716Z bot:balancer Calculating difference between desired value and value before target. Unallocated remainder.
2025-08-26T13:24:54.716Z bot:balancer beforeDiffNumber 1.****************
2025-08-26T13:24:54.716Z bot:balancer Summing up remainders
2025-08-26T13:24:54.716Z bot:balancer How much to buy (can be negative, then need to sell)
2025-08-26T13:24:54.716Z bot:balancer toBuyNumber -237.12
2025-08-26T13:24:54.716Z bot:balancer How many lots to buy (can be negative, then need to sell)
2025-08-26T13:24:54.716Z bot:balancer toBuyLots -24
2025-08-26T13:24:54.716Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:24:54.716Z bot:balancer positionIndex 8
2025-08-26T13:24:54.716Z bot:balancer position {
  pair: 'TBRU/RUB',
  base: 'TBRU',
  quote: 'RUB',
  figi: 'TCS60A1039N1',
  price: { units: 7, nano: 390000000 },
  priceNumber: 7.39,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 7, nano: 390000000 },
  lotPriceNumber: 7.39
}
2025-08-26T13:24:54.716Z bot:balancer Calculating how many rubles the desired share will be with multiplier
2025-08-26T13:24:54.716Z bot:balancer walletSumNumber 383.51
2025-08-26T13:24:54.716Z bot:balancer desiredPercent 1.6280310252728951
2025-08-26T13:24:54.716Z bot:balancer desiredAmountNumber (considering multiplier) 24.97464714009632
2025-08-26T13:24:54.716Z bot:balancer Calculating how many lots can be bought before desired target
2025-08-26T13:24:54.716Z bot:balancer canBuyBeforeTargetLots 3
2025-08-26T13:24:54.716Z bot:balancer Calculating cost of position that can be bought before desired target
2025-08-26T13:24:54.716Z bot:balancer canBuyBeforeTargetNumber 22.169999999999998
2025-08-26T13:24:54.716Z bot:balancer Calculating difference between desired value and value before target. Unallocated remainder.
2025-08-26T13:24:54.716Z bot:balancer beforeDiffNumber 2.8046471400963213
2025-08-26T13:24:54.716Z bot:balancer Summing up remainders
2025-08-26T13:24:54.716Z bot:balancer How much to buy (can be negative, then need to sell)
2025-08-26T13:24:54.716Z bot:balancer How many lots to buy (can be negative, then need to sell)
2025-08-26T13:24:54.716Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:24:54.716Z bot:balancer positionIndex 4
2025-08-26T13:24:54.716Z bot:balancer position {
  pair: 'TDIV@/RUB',
  base: 'TDIV@',
  quote: 'RUB',
  figi: 'TCS10A107563',
  amount: 12,
  lotSize: 1,
  price: { units: 10, nano: ********* },
  priceNumber: 10.54,
  lotPrice: { units: 10, nano: ********* },
  totalPrice: { units: 126, nano: ********* },
  totalPriceNumber: 126.48,
  lotPriceNumber: 10.54
}
2025-08-26T13:24:54.716Z bot:balancer Calculating how many rubles the desired share will be with multiplier
2025-08-26T13:24:54.716Z bot:balancer walletSumNumber 383.51
2025-08-26T13:24:54.716Z bot:balancer desiredPercent 0.3256341541325812
2025-08-26T13:24:54.716Z bot:balancer desiredAmountNumber (considering multiplier) 4.***************
2025-08-26T13:24:54.716Z bot:balancer Calculating how many lots can be bought before desired target
2025-08-26T13:24:54.716Z bot:balancer canBuyBeforeTargetLots 0
2025-08-26T13:24:54.716Z bot:balancer Calculating cost of position that can be bought before desired target
2025-08-26T13:24:54.716Z bot:balancer canBuyBeforeTargetNumber 0
2025-08-26T13:24:54.716Z bot:balancer Calculating difference between desired value and value before target. Unallocated remainder.
2025-08-26T13:24:54.716Z bot:balancer beforeDiffNumber 4.***************
2025-08-26T13:24:54.716Z bot:balancer Summing up remainders
2025-08-26T13:24:54.717Z bot:balancer How much to buy (can be negative, then need to sell)
2025-08-26T13:24:54.717Z bot:balancer toBuyNumber -126.48
2025-08-26T13:24:54.717Z bot:balancer How many lots to buy (can be negative, then need to sell)
2025-08-26T13:24:54.717Z bot:balancer toBuyLots -12
2025-08-26T13:24:54.717Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:24:54.717Z bot:balancer positionIndex 10
2025-08-26T13:24:54.717Z bot:balancer position {
  pair: 'TITR/RUB',
  base: 'TITR',
  quote: 'RUB',
  figi: 'TCS30A108BL2',
  price: { units: 6, nano: 660000000 },
  priceNumber: 6.66,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 6, nano: 660000000 },
  lotPriceNumber: 6.66
}
2025-08-26T13:24:54.717Z bot:balancer Calculating how many rubles the desired share will be with multiplier
2025-08-26T13:24:54.717Z bot:balancer walletSumNumber 383.51
2025-08-26T13:24:54.717Z bot:balancer desiredPercent 0.14531584844753487
2025-08-26T13:24:54.717Z bot:balancer desiredAmountNumber (considering multiplier) 2.229203241524564
2025-08-26T13:24:54.717Z bot:balancer Calculating how many lots can be bought before desired target
2025-08-26T13:24:54.717Z bot:balancer canBuyBeforeTargetLots 0
2025-08-26T13:24:54.717Z bot:balancer Calculating cost of position that can be bought before desired target
2025-08-26T13:24:54.717Z bot:balancer canBuyBeforeTargetNumber 0
2025-08-26T13:24:54.717Z bot:balancer Calculating difference between desired value and value before target. Unallocated remainder.
2025-08-26T13:24:54.717Z bot:balancer beforeDiffNumber 2.229203241524564
2025-08-26T13:24:54.717Z bot:balancer Summing up remainders
2025-08-26T13:24:54.717Z bot:balancer How much to buy (can be negative, then need to sell)
2025-08-26T13:24:54.717Z bot:balancer How many lots to buy (can be negative, then need to sell)
2025-08-26T13:24:54.717Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:24:54.717Z bot:balancer positionIndex 5
2025-08-26T13:24:54.717Z bot:balancer position {
  pair: 'TLCB/RUB',
  base: 'TLCB',
  quote: 'RUB',
  figi: 'TCS20A107597',
  price: { units: 10, nano: 140000000 },
  priceNumber: 10.14,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 10, nano: 140000000 },
  lotPriceNumber: 10.14
}
2025-08-26T13:24:54.717Z bot:balancer Calculating how many rubles the desired share will be with multiplier
2025-08-26T13:24:54.717Z bot:balancer walletSumNumber 383.51
2025-08-26T13:24:54.717Z bot:balancer desiredPercent 3.5149577814013075
2025-08-26T13:24:54.717Z bot:balancer desiredAmountNumber (considering multiplier) 53.92085834980861
2025-08-26T13:24:54.717Z bot:balancer Calculating how many lots can be bought before desired target
2025-08-26T13:24:54.717Z bot:balancer canBuyBeforeTargetLots 5
2025-08-26T13:24:54.717Z bot:balancer Calculating cost of position that can be bought before desired target
2025-08-26T13:24:54.717Z bot:balancer canBuyBeforeTargetNumber 50.7
2025-08-26T13:24:54.717Z bot:balancer Calculating difference between desired value and value before target. Unallocated remainder.
2025-08-26T13:24:54.717Z bot:balancer beforeDiffNumber 3.2208583498086085
2025-08-26T13:24:54.717Z bot:balancer Summing up remainders
2025-08-26T13:24:54.717Z bot:balancer How much to buy (can be negative, then need to sell)
2025-08-26T13:24:54.717Z bot:balancer How many lots to buy (can be negative, then need to sell)
2025-08-26T13:24:54.717Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:24:54.717Z bot:balancer positionIndex 0
2025-08-26T13:24:54.717Z bot:balancer position {
  pair: 'TMON/RUB',
  base: 'TMON',
  quote: 'RUB',
  figi: 'TCS70A106DL2',
  price: { units: 141, nano: 710000000 },
  priceNumber: 141.71,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 141, nano: 710000000 },
  lotPriceNumber: 141.71
}
2025-08-26T13:24:54.717Z bot:balancer Calculating how many rubles the desired share will be with multiplier
2025-08-26T13:24:54.717Z bot:balancer walletSumNumber 383.51
2025-08-26T13:24:54.717Z bot:balancer desiredPercent 66.06997141722876
2025-08-26T13:24:54.717Z bot:balancer desiredAmountNumber (considering multiplier) 1013.539789528856
2025-08-26T13:24:54.717Z bot:balancer Calculating how many lots can be bought before desired target
2025-08-26T13:24:54.717Z bot:balancer canBuyBeforeTargetLots 7
2025-08-26T13:24:54.717Z bot:balancer Calculating cost of position that can be bought before desired target
2025-08-26T13:24:54.717Z bot:balancer canBuyBeforeTargetNumber 991.97
2025-08-26T13:24:54.717Z bot:balancer Calculating difference between desired value and value before target. Unallocated remainder.
2025-08-26T13:24:54.717Z bot:balancer beforeDiffNumber 21.56978952885595
2025-08-26T13:24:54.717Z bot:balancer Summing up remainders
2025-08-26T13:24:54.717Z bot:balancer How much to buy (can be negative, then need to sell)
2025-08-26T13:24:54.717Z bot:balancer How many lots to buy (can be negative, then need to sell)
2025-08-26T13:24:54.717Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:24:54.717Z bot:balancer positionIndex 9
2025-08-26T13:24:54.717Z bot:balancer position {
  pair: 'TMOS@/RUB',
  base: 'TMOS@',
  quote: 'RUB',
  figi: 'TCS60A101X76',
  amount: 19,
  lotSize: 1,
  price: { units: 6, nano: ********* },
  priceNumber: 6.69,
  lotPrice: { units: 6, nano: ********* },
  totalPrice: { units: 127, nano: ********* },
  totalPriceNumber: 127.11,
  lotPriceNumber: 6.69
}
2025-08-26T13:24:54.717Z bot:balancer Calculating how many rubles the desired share will be with multiplier
2025-08-26T13:24:54.717Z bot:balancer walletSumNumber 383.51
2025-08-26T13:24:54.717Z bot:balancer desiredPercent 3.9570906465242537
2025-08-26T13:24:54.717Z bot:balancer desiredAmountNumber (considering multiplier) 60.***************
2025-08-26T13:24:54.717Z bot:balancer Calculating how many lots can be bought before desired target
2025-08-26T13:24:54.717Z bot:balancer canBuyBeforeTargetLots 9
2025-08-26T13:24:54.717Z bot:balancer Calculating cost of position that can be bought before desired target
2025-08-26T13:24:54.717Z bot:balancer canBuyBeforeTargetNumber 60.21
2025-08-26T13:24:54.717Z bot:balancer Calculating difference between desired value and value before target. Unallocated remainder.
2025-08-26T13:24:54.717Z bot:balancer beforeDiffNumber 0.****************
2025-08-26T13:24:54.717Z bot:balancer Summing up remainders
2025-08-26T13:24:54.717Z bot:balancer How much to buy (can be negative, then need to sell)
2025-08-26T13:24:54.717Z bot:balancer toBuyNumber -66.9
2025-08-26T13:24:54.717Z bot:balancer How many lots to buy (can be negative, then need to sell)
2025-08-26T13:24:54.717Z bot:balancer toBuyLots -10
2025-08-26T13:24:54.717Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:24:54.717Z bot:balancer positionIndex 2
2025-08-26T13:24:54.717Z bot:balancer position {
  pair: 'TOFZ/RUB',
  base: 'TOFZ',
  quote: 'RUB',
  figi: 'TCS70A10A1L8',
  price: { units: 13, nano: 280000000 },
  priceNumber: 13.28,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 13, nano: 280000000 },
  lotPriceNumber: 13.28
}
2025-08-26T13:24:54.718Z bot:balancer Calculating how many rubles the desired share will be with multiplier
2025-08-26T13:24:54.718Z bot:balancer walletSumNumber 383.51
2025-08-26T13:24:54.718Z bot:balancer desiredPercent 1.1767286499670446
2025-08-26T13:24:54.718Z bot:balancer desiredAmountNumber (considering multiplier) 18.05148818195445
2025-08-26T13:24:54.718Z bot:balancer Calculating how many lots can be bought before desired target
2025-08-26T13:24:54.718Z bot:balancer canBuyBeforeTargetLots 1
2025-08-26T13:24:54.718Z bot:balancer Calculating cost of position that can be bought before desired target
2025-08-26T13:24:54.718Z bot:balancer canBuyBeforeTargetNumber 13.28
2025-08-26T13:24:54.718Z bot:balancer Calculating difference between desired value and value before target. Unallocated remainder.
2025-08-26T13:24:54.718Z bot:balancer beforeDiffNumber 4.771488181954451
2025-08-26T13:24:54.718Z bot:balancer Summing up remainders
2025-08-26T13:24:54.718Z bot:balancer How much to buy (can be negative, then need to sell)
2025-08-26T13:24:54.718Z bot:balancer How many lots to buy (can be negative, then need to sell)
2025-08-26T13:24:54.718Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:24:54.718Z bot:balancer positionIndex 11
2025-08-26T13:24:54.718Z bot:balancer position {
  pair: 'RUB/RUB',
  base: 'RUB',
  quote: 'RUB',
  figi: undefined,
  amount: -367,
  lotSize: 1,
  price: { units: 1, nano: 0 },
  priceNumber: 1,
  lotPrice: { units: 1, nano: 0 },
  totalPrice: { units: -367, nano: 0 },
  lotPriceNumber: 1,
  totalPriceNumber: -367
}
2025-08-26T13:24:54.718Z bot:balancer Calculating how many rubles the desired share will be with multiplier
2025-08-26T13:24:54.718Z bot:balancer walletSumNumber 383.51
2025-08-26T13:24:54.718Z bot:balancer desiredPercent 0
2025-08-26T13:24:54.718Z bot:balancer desiredAmountNumber (considering multiplier) 0
2025-08-26T13:24:54.718Z bot:balancer Calculating how many lots can be bought before desired target
2025-08-26T13:24:54.718Z bot:balancer canBuyBeforeTargetLots 0
2025-08-26T13:24:54.718Z bot:balancer Calculating cost of position that can be bought before desired target
2025-08-26T13:24:54.718Z bot:balancer canBuyBeforeTargetNumber 0
2025-08-26T13:24:54.718Z bot:balancer Calculating difference between desired value and value before target. Unallocated remainder.
2025-08-26T13:24:54.718Z bot:balancer beforeDiffNumber 0
2025-08-26T13:24:54.718Z bot:balancer Summing up remainders
2025-08-26T13:24:54.718Z bot:balancer How much to buy (can be negative, then need to sell)
2025-08-26T13:24:54.718Z bot:balancer toBuyNumber 367
2025-08-26T13:24:54.718Z bot:balancer How many lots to buy (can be negative, then need to sell)
2025-08-26T13:24:54.718Z bot:balancer toBuyLots 367
2025-08-26T13:24:54.718Z bot:balancer sortedWallet [
  {
    pair: 'TMON/RUB',
    base: 'TMON',
    quote: 'RUB',
    figi: 'TCS70A106DL2',
    price: { units: 141, nano: 710000000 },
    priceNumber: 141.71,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 141, nano: 710000000 },
    lotPriceNumber: 141.71,
    desiredAmountNumber: 1013.539789528856,
    canBuyBeforeTargetLots: 7,
    canBuyBeforeTargetNumber: 991.97,
    beforeDiffNumber: 21.56978952885595
  },
  {
    pair: 'TPAY/RUB',
    base: 'TPAY',
    quote: 'RUB',
    figi: 'TCS00A108WX3',
    price: { units: 101, nano: ********* },
    priceNumber: 101.69,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 101, nano: ********* },
    lotPriceNumber: 101.69,
    desiredAmountNumber: 225.83034351365862,
    canBuyBeforeTargetLots: 2,
    canBuyBeforeTargetNumber: 203.38,
    beforeDiffNumber: 22.450343513658623
  },
  {
    pair: 'TOFZ/RUB',
    base: 'TOFZ',
    quote: 'RUB',
    figi: 'TCS70A10A1L8',
    price: { units: 13, nano: 280000000 },
    priceNumber: 13.28,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 13, nano: 280000000 },
    lotPriceNumber: 13.28,
    desiredAmountNumber: 18.05148818195445,
    canBuyBeforeTargetLots: 1,
    canBuyBeforeTargetNumber: 13.28,
    beforeDiffNumber: 4.771488181954451
  },
  {
    pair: 'TGLD@/RUB',
    base: 'TGLD@',
    quote: 'RUB',
    figi: 'TCS80A101X50',
    amount: 12,
    lotSize: 1,
    price: { units: 10, nano: ********* },
    priceNumber: 10.8,
    lotPrice: { units: 10, nano: ********* },
    totalPrice: { units: 129, nano: ********* },
    totalPriceNumber: 129.6,
    lotPriceNumber: 10.8,
    desiredAmountNumber: 45.**************,
    canBuyBeforeTargetLots: 4,
    canBuyBeforeTargetNumber: 43.2,
    beforeDiffNumber: 1.***************,
    toBuyNumber: -86.**************,
    toBuyLots: -8
  },
  {
    pair: 'TDIV@/RUB',
    base: 'TDIV@',
    quote: 'RUB',
    figi: 'TCS10A107563',
    amount: 12,
    lotSize: 1,
    price: { units: 10, nano: ********* },
    priceNumber: 10.54,
    lotPrice: { units: 10, nano: ********* },
    totalPrice: { units: 126, nano: ********* },
    totalPriceNumber: 126.48,
    lotPriceNumber: 10.54,
    desiredAmountNumber: 4.***************,
    canBuyBeforeTargetLots: 0,
    canBuyBeforeTargetNumber: 0,
    beforeDiffNumber: 4.***************,
    toBuyNumber: -126.48,
    toBuyLots: -12
  },
  {
    pair: 'TLCB/RUB',
    base: 'TLCB',
    quote: 'RUB',
    figi: 'TCS20A107597',
    price: { units: 10, nano: 140000000 },
    priceNumber: 10.14,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 10, nano: 140000000 },
    lotPriceNumber: 10.14,
    desiredAmountNumber: 53.92085834980861,
    canBuyBeforeTargetLots: 5,
    canBuyBeforeTargetNumber: 50.7,
    beforeDiffNumber: 3.2208583498086085
  },
  {
    pair: 'TRND/RUB',
    base: 'TRND',
    quote: 'RUB',
    figi: 'TCS00A10B0G9',
    amount: 24,
    lotSize: 1,
    price: { units: 9, nano: ********* },
    priceNumber: 9.88,
    lotPrice: { units: 9, nano: ********* },
    totalPrice: { units: 237, nano: ********* },
    totalPriceNumber: 237.12,
    lotPriceNumber: 9.88,
    desiredAmountNumber: 1.****************,
    canBuyBeforeTargetLots: 0,
    canBuyBeforeTargetNumber: 0,
    beforeDiffNumber: 1.****************,
    toBuyNumber: -237.12,
    toBuyLots: -24
  },
  {
    pair: 'TRUR/RUB',
    base: 'TRUR',
    quote: 'RUB',
    figi: 'BBG000000001',
    amount: 14,
    lotSize: 1,
    price: { units: 9, nano: ********* },
    priceNumber: 9.3,
    lotPrice: { units: 9, nano: ********* },
    totalPrice: { units: 130, nano: ********* },
    totalPriceNumber: 130.2,
    lotPriceNumber: 9.3,
    desiredAmountNumber: 83.**************,
    canBuyBeforeTargetLots: 8,
    canBuyBeforeTargetNumber: 74.4,
    beforeDiffNumber: 9.***************,
    toBuyNumber: -55.**************,
    toBuyLots: -6
  },
  {
    pair: 'TBRU/RUB',
    base: 'TBRU',
    quote: 'RUB',
    figi: 'TCS60A1039N1',
    price: { units: 7, nano: 390000000 },
    priceNumber: 7.39,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 7, nano: 390000000 },
    lotPriceNumber: 7.39,
    desiredAmountNumber: 24.97464714009632,
    canBuyBeforeTargetLots: 3,
    canBuyBeforeTargetNumber: 22.169999999999998,
    beforeDiffNumber: 2.8046471400963213
  },
  {
    pair: 'TMOS@/RUB',
    base: 'TMOS@',
    quote: 'RUB',
    figi: 'TCS60A101X76',
    amount: 19,
    lotSize: 1,
    price: { units: 6, nano: ********* },
    priceNumber: 6.69,
    lotPrice: { units: 6, nano: ********* },
    totalPrice: { units: 127, nano: ********* },
    totalPriceNumber: 127.11,
    lotPriceNumber: 6.69,
    desiredAmountNumber: 60.***************,
    canBuyBeforeTargetLots: 9,
    canBuyBeforeTargetNumber: 60.21,
    beforeDiffNumber: 0.****************,
    toBuyNumber: -66.9,
    toBuyLots: -10
  },
  {
    pair: 'TITR/RUB',
    base: 'TITR',
    quote: 'RUB',
    figi: 'TCS30A108BL2',
    price: { units: 6, nano: 660000000 },
    priceNumber: 6.66,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 6, nano: 660000000 },
    lotPriceNumber: 6.66,
    desiredAmountNumber: 2.229203241524564,
    canBuyBeforeTargetLots: 0,
    canBuyBeforeTargetNumber: 0,
    beforeDiffNumber: 2.229203241524564
  },
  {
    pair: 'RUB/RUB',
    base: 'RUB',
    quote: 'RUB',
    figi: undefined,
    amount: -367,
    lotSize: 1,
    price: { units: 1, nano: 0 },
    priceNumber: 1,
    lotPrice: { units: 1, nano: 0 },
    totalPrice: { units: -367, nano: 0 },
    lotPriceNumber: 1,
    totalPriceNumber: -367,
    desiredAmountNumber: 0,
    canBuyBeforeTargetLots: 0,
    canBuyBeforeTargetNumber: 0,
    beforeDiffNumber: 0,
    toBuyNumber: 367,
    toBuyLots: 367
  }
]
2025-08-26T13:24:54.720Z bot:balancer ordersPlanned [
  {
    pair: 'TRND/RUB',
    base: 'TRND',
    quote: 'RUB',
    figi: 'TCS00A10B0G9',
    amount: 24,
    lotSize: 1,
    price: { units: 9, nano: ********* },
    priceNumber: 9.88,
    lotPrice: { units: 9, nano: ********* },
    totalPrice: { units: 237, nano: ********* },
    totalPriceNumber: 237.12,
    lotPriceNumber: 9.88,
    desiredAmountNumber: 1.****************,
    canBuyBeforeTargetLots: 0,
    canBuyBeforeTargetNumber: 0,
    beforeDiffNumber: 1.****************,
    toBuyNumber: -237.12,
    toBuyLots: -24
  },
  {
    pair: 'TDIV@/RUB',
    base: 'TDIV@',
    quote: 'RUB',
    figi: 'TCS10A107563',
    amount: 12,
    lotSize: 1,
    price: { units: 10, nano: ********* },
    priceNumber: 10.54,
    lotPrice: { units: 10, nano: ********* },
    totalPrice: { units: 126, nano: ********* },
    totalPriceNumber: 126.48,
    lotPriceNumber: 10.54,
    desiredAmountNumber: 4.***************,
    canBuyBeforeTargetLots: 0,
    canBuyBeforeTargetNumber: 0,
    beforeDiffNumber: 4.***************,
    toBuyNumber: -126.48,
    toBuyLots: -12
  },
  {
    pair: 'TGLD@/RUB',
    base: 'TGLD@',
    quote: 'RUB',
    figi: 'TCS80A101X50',
    amount: 12,
    lotSize: 1,
    price: { units: 10, nano: ********* },
    priceNumber: 10.8,
    lotPrice: { units: 10, nano: ********* },
    totalPrice: { units: 129, nano: ********* },
    totalPriceNumber: 129.6,
    lotPriceNumber: 10.8,
    desiredAmountNumber: 45.**************,
    canBuyBeforeTargetLots: 4,
    canBuyBeforeTargetNumber: 43.2,
    beforeDiffNumber: 1.***************,
    toBuyNumber: -86.**************,
    toBuyLots: -8
  },
  {
    pair: 'TMOS@/RUB',
    base: 'TMOS@',
    quote: 'RUB',
    figi: 'TCS60A101X76',
    amount: 19,
    lotSize: 1,
    price: { units: 6, nano: ********* },
    priceNumber: 6.69,
    lotPrice: { units: 6, nano: ********* },
    totalPrice: { units: 127, nano: ********* },
    totalPriceNumber: 127.11,
    lotPriceNumber: 6.69,
    desiredAmountNumber: 60.***************,
    canBuyBeforeTargetLots: 9,
    canBuyBeforeTargetNumber: 60.21,
    beforeDiffNumber: 0.****************,
    toBuyNumber: -66.9,
    toBuyLots: -10
  },
  {
    pair: 'TRUR/RUB',
    base: 'TRUR',
    quote: 'RUB',
    figi: 'BBG000000001',
    amount: 14,
    lotSize: 1,
    price: { units: 9, nano: ********* },
    priceNumber: 9.3,
    lotPrice: { units: 9, nano: ********* },
    totalPrice: { units: 130, nano: ********* },
    totalPriceNumber: 130.2,
    lotPriceNumber: 9.3,
    desiredAmountNumber: 83.**************,
    canBuyBeforeTargetLots: 8,
    canBuyBeforeTargetNumber: 74.4,
    beforeDiffNumber: 9.***************,
    toBuyNumber: -55.**************,
    toBuyLots: -6
  },
  {
    pair: 'RUB/RUB',
    base: 'RUB',
    quote: 'RUB',
    figi: undefined,
    amount: -367,
    lotSize: 1,
    price: { units: 1, nano: 0 },
    priceNumber: 1,
    lotPrice: { units: 1, nano: 0 },
    totalPrice: { units: -367, nano: 0 },
    lotPriceNumber: 1,
    totalPriceNumber: -367,
    desiredAmountNumber: 0,
    canBuyBeforeTargetLots: 0,
    canBuyBeforeTargetNumber: 0,
    beforeDiffNumber: 0,
    toBuyNumber: 367,
    toBuyLots: 367
  }
]
2025-08-26T13:24:54.721Z bot:balancer walletInfo { remains: 74.72999999999999 }
2025-08-26T13:24:54.721Z bot:balancer Creating necessary orders for all positions
2025-08-26T13:24:54.721Z bot:provider generateOrders
2025-08-26T13:24:54.722Z bot:provider generateOrder
2025-08-26T13:24:54.722Z bot:provider position {
  pair: 'TRND/RUB',
  base: 'TRND',
  quote: 'RUB',
  figi: 'TCS00A10B0G9',
  amount: 24,
  lotSize: 1,
  price: { units: 9, nano: ********* },
  priceNumber: 9.88,
  lotPrice: { units: 9, nano: ********* },
  totalPrice: { units: 237, nano: ********* },
  totalPriceNumber: 237.12,
  lotPriceNumber: 9.88,
  desiredAmountNumber: 1.****************,
  canBuyBeforeTargetLots: 0,
  canBuyBeforeTargetNumber: 0,
  beforeDiffNumber: 1.****************,
  toBuyNumber: -237.12,
  toBuyLots: -24
}
2025-08-26T13:24:54.723Z bot:provider Position is not currency
2025-08-26T13:24:54.723Z bot:provider position.toBuyLots -24
2025-08-26T13:24:54.723Z bot:provider Position is greater than or equal to 1 lot
2025-08-26T13:24:54.723Z bot:provider direction 2
2025-08-26T13:24:54.723Z bot:provider position {
  pair: 'TRND/RUB',
  base: 'TRND',
  quote: 'RUB',
  figi: 'TCS00A10B0G9',
  amount: 24,
  lotSize: 1,
  price: { units: 9, nano: ********* },
  priceNumber: 9.88,
  lotPrice: { units: 9, nano: ********* },
  totalPrice: { units: 237, nano: ********* },
  totalPriceNumber: 237.12,
  lotPriceNumber: 9.88,
  desiredAmountNumber: 1.****************,
  canBuyBeforeTargetLots: 0,
  canBuyBeforeTargetNumber: 0,
  beforeDiffNumber: 1.****************,
  toBuyNumber: -237.12,
  toBuyLots: -24
}
2025-08-26T13:24:54.723Z bot:provider Creating market order
2025-08-26T13:24:54.723Z bot:provider Sending market order {
  accountId: '**********',
  figi: 'TCS00A10B0G9',
  quantity: 24,
  direction: 2,
  orderType: 2,
  orderId: '3s32k1fbomeskvv44'
}
2025-08-26T13:24:55.224Z bot:provider Successfully placed order {
  orderId: '***********',
  executionReportStatus: 1,
  lotsRequested: 24,
  lotsExecuted: 24,
  initialOrderPrice: { currency: 'rub', units: 236, nano: ********* },
  executedOrderPrice: { currency: 'rub', units: 9, nano: ********* },
  totalOrderAmount: { currency: 'rub', units: 236, nano: ********* },
  initialCommission: { currency: 'rub', units: 0, nano: 0 },
  executedCommission: { currency: 'rub', units: 0, nano: 0 },
  aciValue: undefined,
  figi: 'TCS00A10B0G9',
  direction: 2,
  initialSecurityPrice: { currency: 'rub', units: 9, nano: ********* },
  orderType: 2,
  message: '',
  initialOrderPricePt: undefined,
  instrumentUid: 'fa20b747-e414-4922-963a-d19b752535b2',
  orderRequestId: '3s32k1fbomeskvv44',
  responseMetadata: {
    trackingId: 'f06cab54b24e75b794df4b52ea0a3066',
    serverTime: 2025-08-26T13:24:55.228Z
  }
}
2025-08-26T13:25:00.227Z bot:provider generateOrder
2025-08-26T13:25:00.227Z bot:provider position {
  pair: 'TDIV@/RUB',
  base: 'TDIV@',
  quote: 'RUB',
  figi: 'TCS10A107563',
  amount: 12,
  lotSize: 1,
  price: { units: 10, nano: ********* },
  priceNumber: 10.54,
  lotPrice: { units: 10, nano: ********* },
  totalPrice: { units: 126, nano: ********* },
  totalPriceNumber: 126.48,
  lotPriceNumber: 10.54,
  desiredAmountNumber: 4.***************,
  canBuyBeforeTargetLots: 0,
  canBuyBeforeTargetNumber: 0,
  beforeDiffNumber: 4.***************,
  toBuyNumber: -126.48,
  toBuyLots: -12
}
2025-08-26T13:25:00.227Z bot:provider Position is not currency
2025-08-26T13:25:00.227Z bot:provider position.toBuyLots -12
2025-08-26T13:25:00.227Z bot:provider Position is greater than or equal to 1 lot
2025-08-26T13:25:00.227Z bot:provider direction 2
2025-08-26T13:25:00.227Z bot:provider position {
  pair: 'TDIV@/RUB',
  base: 'TDIV@',
  quote: 'RUB',
  figi: 'TCS10A107563',
  amount: 12,
  lotSize: 1,
  price: { units: 10, nano: ********* },
  priceNumber: 10.54,
  lotPrice: { units: 10, nano: ********* },
  totalPrice: { units: 126, nano: ********* },
  totalPriceNumber: 126.48,
  lotPriceNumber: 10.54,
  desiredAmountNumber: 4.***************,
  canBuyBeforeTargetLots: 0,
  canBuyBeforeTargetNumber: 0,
  beforeDiffNumber: 4.***************,
  toBuyNumber: -126.48,
  toBuyLots: -12
}
2025-08-26T13:25:00.227Z bot:provider Creating market order
2025-08-26T13:25:00.227Z bot:provider Sending market order {
  accountId: '**********',
  figi: 'TCS10A107563',
  quantity: 12,
  direction: 2,
  orderType: 2,
  orderId: '3s32k1fbomeskvzcz'
}
2025-08-26T13:25:00.768Z bot:provider Successfully placed order {
  orderId: '************',
  executionReportStatus: 1,
  lotsRequested: 12,
  lotsExecuted: 12,
  initialOrderPrice: { currency: 'rub', units: 125, nano: ********* },
  executedOrderPrice: { currency: 'rub', units: 10, nano: ********* },
  totalOrderAmount: { currency: 'rub', units: 126, nano: ********* },
  initialCommission: { currency: 'rub', units: 0, nano: 0 },
  executedCommission: { currency: 'rub', units: 0, nano: 0 },
  aciValue: undefined,
  figi: 'TCS10A107563',
  direction: 2,
  initialSecurityPrice: { currency: 'rub', units: 10, nano: ********* },
  orderType: 2,
  message: '',
  initialOrderPricePt: undefined,
  instrumentUid: 'd5cba263-cda7-440c-a21d-134fb5d334f6',
  orderRequestId: '3s32k1fbomeskvzcz',
  responseMetadata: {
    trackingId: '6d2d044f804215a3862d92696d3bdc23',
    serverTime: 2025-08-26T13:25:00.765Z
  }
}
2025-08-26T13:25:05.770Z bot:provider generateOrder
2025-08-26T13:25:05.770Z bot:provider position {
  pair: 'TGLD@/RUB',
  base: 'TGLD@',
  quote: 'RUB',
  figi: 'TCS80A101X50',
  amount: 12,
  lotSize: 1,
  price: { units: 10, nano: ********* },
  priceNumber: 10.8,
  lotPrice: { units: 10, nano: ********* },
  totalPrice: { units: 129, nano: ********* },
  totalPriceNumber: 129.6,
  lotPriceNumber: 10.8,
  desiredAmountNumber: 45.**************,
  canBuyBeforeTargetLots: 4,
  canBuyBeforeTargetNumber: 43.2,
  beforeDiffNumber: 1.***************,
  toBuyNumber: -86.**************,
  toBuyLots: -8
}
2025-08-26T13:25:05.770Z bot:provider Position is not currency
2025-08-26T13:25:05.770Z bot:provider position.toBuyLots -8
2025-08-26T13:25:05.770Z bot:provider Position is greater than or equal to 1 lot
2025-08-26T13:25:05.770Z bot:provider direction 2
2025-08-26T13:25:05.770Z bot:provider position {
  pair: 'TGLD@/RUB',
  base: 'TGLD@',
  quote: 'RUB',
  figi: 'TCS80A101X50',
  amount: 12,
  lotSize: 1,
  price: { units: 10, nano: ********* },
  priceNumber: 10.8,
  lotPrice: { units: 10, nano: ********* },
  totalPrice: { units: 129, nano: ********* },
  totalPriceNumber: 129.6,
  lotPriceNumber: 10.8,
  desiredAmountNumber: 45.**************,
  canBuyBeforeTargetLots: 4,
  canBuyBeforeTargetNumber: 43.2,
  beforeDiffNumber: 1.***************,
  toBuyNumber: -86.**************,
  toBuyLots: -8
}
2025-08-26T13:25:05.770Z bot:provider Creating market order
2025-08-26T13:25:05.770Z bot:provider Sending market order {
  accountId: '**********',
  figi: 'TCS80A101X50',
  quantity: 8,
  direction: 2,
  orderType: 2,
  orderId: '3s32k1fbomeskw3my'
}
2025-08-26T13:25:06.274Z bot:provider Successfully placed order {
  orderId: '************',
  executionReportStatus: 1,
  lotsRequested: 8,
  lotsExecuted: 8,
  initialOrderPrice: { currency: 'rub', units: 86, nano: 0 },
  executedOrderPrice: { currency: 'rub', units: 10, nano: ********* },
  totalOrderAmount: { currency: 'rub', units: 86, nano: ********* },
  initialCommission: { currency: 'rub', units: 0, nano: 0 },
  executedCommission: { currency: 'rub', units: 0, nano: 0 },
  aciValue: undefined,
  figi: 'TCS80A101X50',
  direction: 2,
  initialSecurityPrice: { currency: 'rub', units: 10, nano: ********* },
  orderType: 2,
  message: '',
  initialOrderPricePt: undefined,
  instrumentUid: 'de82be66-3b9b-4612-9572-61e3c6039013',
  orderRequestId: '3s32k1fbomeskw3my',
  responseMetadata: {
    trackingId: 'e55ccf43a90eaf08287586cc192ffb06',
    serverTime: 2025-08-26T13:25:06.282Z
  }
}
2025-08-26T13:25:11.277Z bot:provider generateOrder
2025-08-26T13:25:11.277Z bot:provider position {
  pair: 'TMOS@/RUB',
  base: 'TMOS@',
  quote: 'RUB',
  figi: 'TCS60A101X76',
  amount: 19,
  lotSize: 1,
  price: { units: 6, nano: ********* },
  priceNumber: 6.69,
  lotPrice: { units: 6, nano: ********* },
  totalPrice: { units: 127, nano: ********* },
  totalPriceNumber: 127.11,
  lotPriceNumber: 6.69,
  desiredAmountNumber: 60.***************,
  canBuyBeforeTargetLots: 9,
  canBuyBeforeTargetNumber: 60.21,
  beforeDiffNumber: 0.****************,
  toBuyNumber: -66.9,
  toBuyLots: -10
}
2025-08-26T13:25:11.277Z bot:provider Position is not currency
2025-08-26T13:25:11.277Z bot:provider position.toBuyLots -10
2025-08-26T13:25:11.277Z bot:provider Position is greater than or equal to 1 lot
2025-08-26T13:25:11.277Z bot:provider direction 2
2025-08-26T13:25:11.277Z bot:provider position {
  pair: 'TMOS@/RUB',
  base: 'TMOS@',
  quote: 'RUB',
  figi: 'TCS60A101X76',
  amount: 19,
  lotSize: 1,
  price: { units: 6, nano: ********* },
  priceNumber: 6.69,
  lotPrice: { units: 6, nano: ********* },
  totalPrice: { units: 127, nano: ********* },
  totalPriceNumber: 127.11,
  lotPriceNumber: 6.69,
  desiredAmountNumber: 60.***************,
  canBuyBeforeTargetLots: 9,
  canBuyBeforeTargetNumber: 60.21,
  beforeDiffNumber: 0.****************,
  toBuyNumber: -66.9,
  toBuyLots: -10
}
2025-08-26T13:25:11.278Z bot:provider Creating market order
2025-08-26T13:25:11.278Z bot:provider Sending market order {
  accountId: '**********',
  figi: 'TCS60A101X76',
  quantity: 10,
  direction: 2,
  orderType: 2,
  orderId: '3s32k1fbomeskw7vy'
}
2025-08-26T13:25:11.773Z bot:provider Successfully placed order {
  orderId: '************',
  executionReportStatus: 1,
  lotsRequested: 10,
  lotsExecuted: 10,
  initialOrderPrice: { currency: 'rub', units: 66, nano: ********* },
  executedOrderPrice: { currency: 'rub', units: 6, nano: ********* },
  totalOrderAmount: { currency: 'rub', units: 66, nano: ********* },
  initialCommission: { currency: 'rub', units: 0, nano: 0 },
  executedCommission: { currency: 'rub', units: 0, nano: 0 },
  aciValue: undefined,
  figi: 'TCS60A101X76',
  direction: 2,
  initialSecurityPrice: { currency: 'rub', units: 6, nano: ********* },
  orderType: 2,
  message: '',
  initialOrderPricePt: undefined,
  instrumentUid: 'f509af83-6e71-462f-901f-bcb073f6773b',
  orderRequestId: '3s32k1fbomeskw7vy',
  responseMetadata: {
    trackingId: 'f187d605679fd58acae3a39505bda74f',
    serverTime: 2025-08-26T13:25:11.776Z
  }
}
2025-08-26T13:25:16.775Z bot:provider generateOrder
2025-08-26T13:25:16.775Z bot:provider position {
  pair: 'TRUR/RUB',
  base: 'TRUR',
  quote: 'RUB',
  figi: 'BBG000000001',
  amount: 14,
  lotSize: 1,
  price: { units: 9, nano: ********* },
  priceNumber: 9.3,
  lotPrice: { units: 9, nano: ********* },
  totalPrice: { units: 130, nano: ********* },
  totalPriceNumber: 130.2,
  lotPriceNumber: 9.3,
  desiredAmountNumber: 83.**************,
  canBuyBeforeTargetLots: 8,
  canBuyBeforeTargetNumber: 74.4,
  beforeDiffNumber: 9.***************,
  toBuyNumber: -55.**************,
  toBuyLots: -6
}
2025-08-26T13:25:16.776Z bot:provider Position is not currency
2025-08-26T13:25:16.776Z bot:provider position.toBuyLots -6
2025-08-26T13:25:16.776Z bot:provider Position is greater than or equal to 1 lot
2025-08-26T13:25:16.776Z bot:provider direction 2
2025-08-26T13:25:16.776Z bot:provider position {
  pair: 'TRUR/RUB',
  base: 'TRUR',
  quote: 'RUB',
  figi: 'BBG000000001',
  amount: 14,
  lotSize: 1,
  price: { units: 9, nano: ********* },
  priceNumber: 9.3,
  lotPrice: { units: 9, nano: ********* },
  totalPrice: { units: 130, nano: ********* },
  totalPriceNumber: 130.2,
  lotPriceNumber: 9.3,
  desiredAmountNumber: 83.**************,
  canBuyBeforeTargetLots: 8,
  canBuyBeforeTargetNumber: 74.4,
  beforeDiffNumber: 9.***************,
  toBuyNumber: -55.**************,
  toBuyLots: -6
}
2025-08-26T13:25:16.776Z bot:provider Creating market order
2025-08-26T13:25:16.776Z bot:provider Sending market order {
  accountId: '**********',
  figi: 'BBG000000001',
  quantity: 6,
  direction: 2,
  orderType: 2,
  orderId: '3s32k1fbomeskwc4o'
}
2025-08-26T13:25:17.265Z bot:provider Successfully placed order {
  orderId: '***********',
  executionReportStatus: 1,
  lotsRequested: 6,
  lotsExecuted: 6,
  initialOrderPrice: { currency: 'rub', units: 55, nano: ********* },
  executedOrderPrice: { currency: 'rub', units: 9, nano: ********* },
  totalOrderAmount: { currency: 'rub', units: 55, nano: ********* },
  initialCommission: { currency: 'rub', units: 0, nano: 0 },
  executedCommission: { currency: 'rub', units: 0, nano: 0 },
  aciValue: undefined,
  figi: 'BBG000000001',
  direction: 2,
  initialSecurityPrice: { currency: 'rub', units: 9, nano: ********* },
  orderType: 2,
  message: '',
  initialOrderPricePt: undefined,
  instrumentUid: 'e2d0dbac-d354-4c36-a5ed-e5aae42ffc76',
  orderRequestId: '3s32k1fbomeskwc4o',
  responseMetadata: {
    trackingId: '4dbfbe397dbc2b6781a477d060a352f8',
    serverTime: 2025-08-26T13:25:17.277Z
  }
}
2025-08-26T13:25:22.268Z bot:provider generateOrder
2025-08-26T13:25:22.268Z bot:provider position {
  pair: 'RUB/RUB',
  base: 'RUB',
  quote: 'RUB',
  figi: undefined,
  amount: -367,
  lotSize: 1,
  price: { units: 1, nano: 0 },
  priceNumber: 1,
  lotPrice: { units: 1, nano: 0 },
  totalPrice: { units: -367, nano: 0 },
  lotPriceNumber: 1,
  totalPriceNumber: -367,
  desiredAmountNumber: 0,
  canBuyBeforeTargetLots: 0,
  canBuyBeforeTargetNumber: 0,
  beforeDiffNumber: 0,
  toBuyNumber: 367,
  toBuyLots: 367
}
2025-08-26T13:25:22.268Z bot:provider If position is RUB, do nothing
BALANCING RESULT:
Format: TICKER: diff: before% -> after% (target%)
Where: before% = current share, after% = actual share after balancing, (target%) = target from balancer, diff = change in percentage points

TRND: +0.00%: 31.59% -> 31.59% (0.00%)
TRUR: 0%: 17.35% -> 17.35% (41.84%)
TGLD: -0.00%: 17.27% -> 17.27% (24.30%)
TMOS: 0%: 16.94% -> 16.94% (33.86%)
TDIV: +0.00%: 16.85% -> 16.85% (0.00%)
TMON: 0%: 0.00% -> 0.00% (0.00%)
TPAY: 0%: 0.00% -> 0.00% (0.00%)
TOFZ: 0%: 0.00% -> 0.00% (0.00%)
TLCB: 0%: 0.00% -> 0.00% (0.00%)
TBRU: 0%: 0.00% -> 0.00% (0.00%)
TITR: 0%: 0.00% -> 0.00% (0.00%)
RUR: -367.00 RUB
2025-08-26T13:25:22.278Z bot:provider ITERATION #1 FINISHED. TIME: Tue Aug 26 2025 16:25:22 GMT+0300 (Moscow Standard Time)
