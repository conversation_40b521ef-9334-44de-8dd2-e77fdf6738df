$ DEBUG=bot:main,bot:provider,bot:balancer bun run ./src/index.ts
28 |       // Configuration validation
29 |       this.validateConfig(this.config);
30 | 
31 |       return this.config;
32 |     } catch (error) {
33 |       throw new Error(`Configuration loading error: ${error instanceof Error ? error.message : 'Unknown error'}`);
                 ^
error: Configuration loading error: JSON Parse error: Expected '}'
      at loadConfig (/Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/src/configLoader.ts:33:13)
      at getAccountById (/Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/src/configLoader.ts:38:25)
      at getAccountConfig (/Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/src/balancer/index.ts:22:32)
      at /Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/src/balancer/index.ts:32:23
      at loadAndEvaluateModule (2:1)

Bun v1.2.20 (macOS arm64)
error: script "dev" exited with code 1
