{"name": "deep-tinkoff-invest-api", "version": "1.0.0", "description": "Trading bot", "type": "module", "repository": {"type": "git", "url": "https://github.com/suenot/deep-tinkoff-invest-api.git"}, "keywords": ["trading", "investment", "strategy", "bot"], "scripts": {"start": "bun run ./src/index.ts", "dev": "DEBUG=bot:main,bot:provider,bot:balancer bun run ./src/index.ts", "accounts": "bun run ./src/index.ts --list-accounts", "config": "bun run ./src/tools/configManager.ts", "etf-cap": "bun run ./src/tools/etfCap.ts", "demo:detailed-output": "bun run ./src/tools/demoDetailedOutput.ts", "test:balancer-logic": "bun run ./src/tools/testBalancerLogic.ts", "scrape:tbank:news": "bun run ./src/tools/scrapeTbankNews.ts TRUR --limit=10", "analyze:news": "bun run ./src/tools/analyzeNews.ts TRUR --limit=10", "update:shares": "bun run ./src/tools/updateSharesCount.ts TRUR", "poll:metrics": "bun run ./src/tools/pollEtfMetrics.ts TGLD,TRAY,TRUR,TRND,TLCB,TOFZ,TBRU,TMON,TMOS,TITR,TDIV --once", "poll:metrics:all": "bun run ./src/tools/pollEtfMetrics.ts TGLD,TSPV,TBRU,TUSD,TEUR,TEMS,TSPX,TEUS,TBUY,TBEU,TRUR,TPAS,TBIO,TCBR,TECH,TSST,TGRN,TSOX,TRAI,TIPO,TFNX,TMOS,TLCB,TOFZ,TMON,TITR,TDIV --once", "test": "bun test --timeout 10000", "test:watch": "bun test --watch", "test:coverage": "bun test --coverage", "test:utils": "bun test src/__tests__/utils/", "test:balancer": "bun test src/__tests__/balancer/", "test:margin": "bun run ./src/balancer/testMargin.ts", "test:margin:disabled": "bun run ./src/balancer/testMarginDisabled.ts", "test:margin:config": "bun run ./src/balancer/testMarginConfig.ts", "build": "bun build ./src/index.ts --outdir ./dist --target bun", "build:optimized": "bun build ./src/index.ts --outdir ./dist --target bun --minify --sourcemap --splitting", "build:analyze": "bun build ./src/index.ts --outdir ./dist --target bun --analyze", "clean": "rm -rf dist node_modules bun.lockb", "perf:etf-cap": "time bun run etf-cap", "perf:build": "time bun run build", "perf:test": "time bun test"}, "author": "<PERSON><PERSON>", "license": "Apache-2.0", "types": "./src/index.d.ts", "dependencies": {"@graphql-tools/schema": "^7.1.5", "@types/mocha": "^5.2.7", "@types/node": "^12.12.8", "@types/ws": "^8.2.2", "apollo-server": "^3.7.0", "apollo-server-core": "3.6.2", "apollo-server-express": "3.6.2", "chai": "^4.2.0", "debug": "^4.3.4", "dotenv": "^16.0.1", "express": "^4.17.1", "graphql": "^15.5.1", "graphql-subscriptions": "^1.2.1", "graphql-tag": "^2.10.1", "graphql-ws": "^5.5.5", "lodash": "^4.17.21", "mocha": "^6.2.2", "nice-grpc": "^2.1.7", "puppeteer": "^24.16.0", "react": "^18.1.0", "request": "^2.88.0", "request-promise": "^4.2.5", "subscriptions-transport-ws": "^0.9.19", "tinkoff-sdk-grpc-js": "^1.8.0-fix", "ts-mocha": "^6.0.0", "tslint": "^5.20.1", "tslint-config-airbnb": "^5.11.2", "tslint-eslint-rules": "^5.4.0", "typescript": "^3.8.3", "uniqid": "^5.1.0", "ws": "^8.4.2"}, "devDependencies": {"@types/lodash": "^4.17.20", "@types/uniqid": "^5.3.4", "bun-types": "latest"}, "engines": {"bun": ">=1.0.0"}}