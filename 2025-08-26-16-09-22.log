$ DEBUG=bot:main,bot:provider,bot:balancer bun run ./src/index.ts
2025-08-26T13:09:22.815Z bot:main main start
2025-08-26T13:09:22.816Z bot:provider Getting accounts list
2025-08-26T13:09:23.799Z bot:provider accountsResponse {
  accounts: [
    {
      id: '**********',
      type: 1,
      name: 'Брокерский счет 4',
      status: 2,
      openedDate: 2025-08-08T00:00:00.000Z,
      closedDate: 1970-01-01T00:00:00.000Z,
      accessLevel: 1
    }
  ]
}
2025-08-26T13:09:23.802Z bot:provider Selected account by index {
  id: '**********',
  type: 1,
  name: 'Брокерский счет 4',
  status: 2,
  openedDate: 2025-08-08T00:00:00.000Z,
  closedDate: 1970-01-01T00:00:00.000Z,
  accessLevel: 1
}
2025-08-26T13:09:23.803Z bot:provider Getting shares list
2025-08-26T13:09:30.432Z bot:provider shares count 1941
2025-08-26T13:09:35.436Z bot:provider Getting ETFs list
2025-08-26T13:09:36.737Z bot:provider etfs count 276
2025-08-26T13:09:41.741Z bot:provider Getting bonds list
2025-08-26T13:09:49.500Z bot:provider bonds count 1392
2025-08-26T13:09:54.504Z bot:provider Getting currencies list
2025-08-26T13:09:54.742Z bot:provider currencies count 16
2025-08-26T13:09:59.746Z bot:provider Getting futures list
2025-08-26T13:10:01.424Z bot:provider futures count 342
2025-08-26T13:10:06.427Z bot:provider =========================
2025-08-26T13:10:06.441Z bot:provider Checking trading schedule for MOEX. Current time: 2025-08-26T13:10:06.440Z
2025-08-26T13:10:06.441Z bot:provider Request params: from=2025-08-26T13:10:06.440Z, to=2025-08-27T13:10:06.440Z
2025-08-26T13:10:06.689Z bot:provider Trading schedules response: {
  "exchanges": [
    {
      "exchange": "MOEX",
      "days": [
        {
          "date": "2025-08-26T00:00:00.000Z",
          "isTradingDay": true,
          "startTime": "2025-08-26T07:00:00.000Z",
          "endTime": "2025-08-26T15:39:59.000Z",
          "openingAuctionStartTime": "2025-08-26T06:50:00.000Z",
          "closingAuctionEndTime": "2025-08-26T15:50:00.000Z",
          "closingAuctionStartTime": "2025-08-26T15:40:01.000Z",
          "openingAuctionEndTime": "2025-08-26T06:59:59.000Z",
          "intervals": [
            {
              "type": "regular_trading_session",
              "interval": {
                "startTs": "2025-08-26T07:00:00.000Z",
                "endTs": "2025-08-26T15:39:59.000Z"
              }
            },
            {
              "type": "regular_trading_session_main",
              "interval": {
                "startTs": "2025-08-26T07:00:00.000Z",
                "endTs": "2025-08-26T15:39:59.000Z"
              }
            },
            {
              "type": "opening_auction",
              "interval": {
                "startTs": "2025-08-26T06:50:00.000Z",
                "endTs": "2025-08-26T06:59:59.000Z"
              }
            },
            {
              "type": "opening_auction_main",
              "interval": {
                "startTs": "2025-08-26T06:50:00.000Z",
                "endTs": "2025-08-26T06:59:59.000Z"
              }
            },
            {
              "type": "closing_auction",
              "interval": {
                "startTs": "2025-08-26T15:40:01.000Z",
                "endTs": "2025-08-26T15:50:00.000Z"
              }
            },
            {
              "type": "closing_auction_main",
              "interval": {
                "startTs": "2025-08-26T15:40:01.000Z",
                "endTs": "2025-08-26T15:50:00.000Z"
              }
            }
          ]
        },
        {
          "date": "2025-08-27T00:00:00.000Z",
          "isTradingDay": true,
          "startTime": "2025-08-27T07:00:00.000Z",
          "endTime": "2025-08-27T15:39:59.000Z",
          "openingAuctionStartTime": "2025-08-27T06:50:00.000Z",
          "closingAuctionEndTime": "2025-08-27T15:50:00.000Z",
          "closingAuctionStartTime": "2025-08-27T15:40:01.000Z",
          "openingAuctionEndTime": "2025-08-27T06:59:59.000Z",
          "intervals": [
            {
              "type": "regular_trading_session",
              "interval": {
                "startTs": "2025-08-27T07:00:00.000Z",
                "endTs": "2025-08-27T15:39:59.000Z"
              }
            },
            {
              "type": "regular_trading_session_main",
              "interval": {
                "startTs": "2025-08-27T07:00:00.000Z",
                "endTs": "2025-08-27T15:39:59.000Z"
              }
            },
            {
              "type": "opening_auction",
              "interval": {
                "startTs": "2025-08-27T06:50:00.000Z",
                "endTs": "2025-08-27T06:59:59.000Z"
              }
            },
            {
              "type": "opening_auction_main",
              "interval": {
                "startTs": "2025-08-27T06:50:00.000Z",
                "endTs": "2025-08-27T06:59:59.000Z"
              }
            },
            {
              "type": "closing_auction",
              "interval": {
                "startTs": "2025-08-27T15:40:01.000Z",
                "endTs": "2025-08-27T15:50:00.000Z"
              }
            },
            {
              "type": "closing_auction_main",
              "interval": {
                "startTs": "2025-08-27T15:40:01.000Z",
                "endTs": "2025-08-27T15:50:00.000Z"
              }
            }
          ]
        }
      ]
    }
  ]
}
2025-08-26T13:10:06.689Z bot:provider Found 2 trading days in schedule
2025-08-26T13:10:06.689Z bot:provider Processing day: {
  "date": "2025-08-26T00:00:00.000Z",
  "isTradingDay": true,
  "startTime": "2025-08-26T07:00:00.000Z",
  "endTime": "2025-08-26T15:39:59.000Z",
  "openingAuctionStartTime": "2025-08-26T06:50:00.000Z",
  "closingAuctionEndTime": "2025-08-26T15:50:00.000Z",
  "closingAuctionStartTime": "2025-08-26T15:40:01.000Z",
  "openingAuctionEndTime": "2025-08-26T06:59:59.000Z",
  "intervals": [
    {
      "type": "regular_trading_session",
      "interval": {
        "startTs": "2025-08-26T07:00:00.000Z",
        "endTs": "2025-08-26T15:39:59.000Z"
      }
    },
    {
      "type": "regular_trading_session_main",
      "interval": {
        "startTs": "2025-08-26T07:00:00.000Z",
        "endTs": "2025-08-26T15:39:59.000Z"
      }
    },
    {
      "type": "opening_auction",
      "interval": {
        "startTs": "2025-08-26T06:50:00.000Z",
        "endTs": "2025-08-26T06:59:59.000Z"
      }
    },
    {
      "type": "opening_auction_main",
      "interval": {
        "startTs": "2025-08-26T06:50:00.000Z",
        "endTs": "2025-08-26T06:59:59.000Z"
      }
    },
    {
      "type": "closing_auction",
      "interval": {
        "startTs": "2025-08-26T15:40:01.000Z",
        "endTs": "2025-08-26T15:50:00.000Z"
      }
    },
    {
      "type": "closing_auction_main",
      "interval": {
        "startTs": "2025-08-26T15:40:01.000Z",
        "endTs": "2025-08-26T15:50:00.000Z"
      }
    }
  ]
}
2025-08-26T13:10:06.689Z bot:provider Session times: start=2025-08-26T07:00:00.000Z, end=2025-08-26T15:39:59.000Z
2025-08-26T13:10:06.689Z bot:provider Evening session: start=undefined, end=undefined
2025-08-26T13:10:06.689Z bot:provider Current time is within main trading session
2025-08-26T13:10:06.689Z bot:provider Getting portfolio
2025-08-26T13:10:06.976Z bot:provider portfolio {
  totalAmountShares: { currency: 'rub', units: 0, nano: 0 },
  totalAmountBonds: { currency: 'rub', units: 0, nano: 0 },
  totalAmountEtf: { currency: 'rub', units: 744, nano: 0 },
  totalAmountCurrencies: { currency: 'rub', units: -360, nano: -********* },
  totalAmountFutures: { currency: 'rub', units: 0, nano: 0 },
  expectedYield: { units: 0, nano: -640000000 },
  positions: [
    {
      figi: 'RUB000UTSTOM',
      instrumentType: 'currency',
      quantity: [Object],
      averagePositionPrice: [Object],
      expectedYield: [Object],
      currentNkd: undefined,
      averagePositionPricePt: [Object],
      currentPrice: [Object],
      averagePositionPriceFifo: [Object],
      quantityLots: [Object],
      blocked: false,
      blockedLots: [Object],
      positionUid: '33e24a92-aab0-409c-88b8-f2d57415b920',
      instrumentUid: 'a92e2e25-a698-45cc-a781-167cf465257c',
      varMargin: [Object],
      expectedYieldFifo: [Object],
      dailyYield: [Object]
    },
    {
      figi: 'TCS10A107563',
      instrumentType: 'etf',
      quantity: [Object],
      averagePositionPrice: [Object],
      expectedYield: [Object],
      currentNkd: undefined,
      averagePositionPricePt: [Object],
      currentPrice: [Object],
      averagePositionPriceFifo: [Object],
      quantityLots: [Object],
      blocked: false,
      blockedLots: [Object],
      positionUid: '8f747dc6-21bf-4f3a-878e-834880728a29',
      instrumentUid: 'd5cba263-cda7-440c-a21d-134fb5d334f6',
      varMargin: [Object],
      expectedYieldFifo: [Object],
      dailyYield: [Object]
    },
    {
      figi: 'TCS80A101X50',
      instrumentType: 'etf',
      quantity: [Object],
      averagePositionPrice: [Object],
      expectedYield: [Object],
      currentNkd: undefined,
      averagePositionPricePt: [Object],
      currentPrice: [Object],
      averagePositionPriceFifo: [Object],
      quantityLots: [Object],
      blocked: false,
      blockedLots: [Object],
      positionUid: '46dd8924-24e9-4581-afd2-97e0ca89009c',
      instrumentUid: 'de82be66-3b9b-4612-9572-61e3c6039013',
      varMargin: [Object],
      expectedYieldFifo: [Object],
      dailyYield: [Object]
    },
    {
      figi: 'BBG000000001',
      instrumentType: 'etf',
      quantity: [Object],
      averagePositionPrice: [Object],
      expectedYield: [Object],
      currentNkd: undefined,
      averagePositionPricePt: [Object],
      currentPrice: [Object],
      averagePositionPriceFifo: [Object],
      quantityLots: [Object],
      blocked: false,
      blockedLots: [Object],
      positionUid: '8005e2ec-66b3-49ae-9711-a424d9c9b61b',
      instrumentUid: 'e2d0dbac-d354-4c36-a5ed-e5aae42ffc76',
      varMargin: [Object],
      expectedYieldFifo: [Object],
      dailyYield: [Object]
    },
    {
      figi: 'TCS60A101X76',
      instrumentType: 'etf',
      quantity: [Object],
      averagePositionPrice: [Object],
      expectedYield: [Object],
      currentNkd: undefined,
      averagePositionPricePt: [Object],
      currentPrice: [Object],
      averagePositionPriceFifo: [Object],
      quantityLots: [Object],
      blocked: false,
      blockedLots: [Object],
      positionUid: 'ffd48f38-aa9b-491c-8e40-140a3f0bc931',
      instrumentUid: 'f509af83-6e71-462f-901f-bcb073f6773b',
      varMargin: [Object],
      expectedYieldFifo: [Object],
      dailyYield: [Object]
    },
    {
      figi: 'TCS00A10B0G9',
      instrumentType: 'etf',
      quantity: [Object],
      averagePositionPrice: [Object],
      expectedYield: [Object],
      currentNkd: undefined,
      averagePositionPricePt: [Object],
      currentPrice: [Object],
      averagePositionPriceFifo: [Object],
      quantityLots: [Object],
      blocked: false,
      blockedLots: [Object],
      positionUid: '0599cd56-02b0-4ceb-8045-eae66523979e',
      instrumentUid: 'fa20b747-e414-4922-963a-d19b752535b2',
      varMargin: [Object],
      expectedYieldFifo: [Object],
      dailyYield: [Object]
    }
  ],
  accountId: '**********',
  totalAmountOptions: { currency: 'rub', units: 0, nano: 0 },
  totalAmountSp: { currency: 'rub', units: 0, nano: 0 },
  totalAmountPortfolio: { currency: 'rub', units: 383, nano: ******** },
  virtualPositions: [],
  dailyYield: { currency: 'rub', units: 0, nano: 7******** },
  dailyYieldRelative: { units: 0, nano: ********* }
}
2025-08-26T13:10:06.978Z bot:provider portfolioPositions [
  {
    figi: 'RUB000UTSTOM',
    instrumentType: 'currency',
    quantity: { units: -360, nano: -********* },
    averagePositionPrice: { currency: 'rub', units: 1, nano: 0 },
    expectedYield: { units: 0, nano: 0 },
    currentNkd: undefined,
    averagePositionPricePt: { units: 0, nano: 0 },
    currentPrice: { currency: 'rub', units: 1, nano: 0 },
    averagePositionPriceFifo: { currency: 'rub', units: 1, nano: 0 },
    quantityLots: { units: -360, nano: -********* },
    blocked: false,
    blockedLots: { units: 0, nano: 0 },
    positionUid: '33e24a92-aab0-409c-88b8-f2d57415b920',
    instrumentUid: 'a92e2e25-a698-45cc-a781-167cf465257c',
    varMargin: { currency: '', units: 0, nano: 0 },
    expectedYieldFifo: { units: 0, nano: 0 },
    dailyYield: { currency: 'rub', units: 0, nano: 0 }
  },
  {
    figi: 'TCS10A107563',
    instrumentType: 'etf',
    quantity: { units: 12, nano: 0 },
    averagePositionPrice: { currency: 'rub', units: 10, nano: 640000000 },
    expectedYield: { units: 0, nano: -720000000 },
    currentNkd: undefined,
    averagePositionPricePt: { units: 0, nano: 0 },
    currentPrice: { currency: 'rub', units: 10, nano: 550000000 },
    averagePositionPriceFifo: { currency: 'rub', units: 10, nano: 610000000 },
    quantityLots: { units: 12, nano: 0 },
    blocked: false,
    blockedLots: { units: 0, nano: 0 },
    positionUid: '8f747dc6-21bf-4f3a-878e-834880728a29',
    instrumentUid: 'd5cba263-cda7-440c-a21d-134fb5d334f6',
    varMargin: { currency: '', units: 0, nano: 0 },
    expectedYieldFifo: { units: 0, nano: -720000000 },
    dailyYield: { currency: 'rub', units: 0, nano: 240000000 }
  },
  {
    figi: 'TCS80A101X50',
    instrumentType: 'etf',
    quantity: { units: 12, nano: 0 },
    averagePositionPrice: { currency: 'rub', units: 10, nano: 750000000 },
    expectedYield: { units: 0, nano: 3******** },
    currentNkd: undefined,
    averagePositionPricePt: { units: 0, nano: 0 },
    currentPrice: { currency: 'rub', units: 10, nano: 790000000 },
    averagePositionPriceFifo: { currency: 'rub', units: 10, nano: 760000000 },
    quantityLots: { units: 12, nano: 0 },
    blocked: false,
    blockedLots: { units: 0, nano: 0 },
    positionUid: '46dd8924-24e9-4581-afd2-97e0ca89009c',
    instrumentUid: 'de82be66-3b9b-4612-9572-61e3c6039013',
    varMargin: { currency: '', units: 0, nano: 0 },
    expectedYieldFifo: { units: 0, nano: 3******** },
    dailyYield: { currency: 'rub', units: 0, nano: 320000000 }
  },
  {
    figi: 'BBG000000001',
    instrumentType: 'etf',
    quantity: { units: 14, nano: 0 },
    averagePositionPrice: { currency: 'rub', units: 9, nano: 320000000 },
    expectedYield: { units: 0, nano: -1******** },
    currentNkd: undefined,
    averagePositionPricePt: { units: 0, nano: 0 },
    currentPrice: { currency: 'rub', units: 9, nano: 300000000 },
    averagePositionPriceFifo: { currency: 'rub', units: 9, nano: 310000000 },
    quantityLots: { units: 14, nano: 0 },
    blocked: false,
    blockedLots: { units: 0, nano: 0 },
    positionUid: '8005e2ec-66b3-49ae-9711-a424d9c9b61b',
    instrumentUid: 'e2d0dbac-d354-4c36-a5ed-e5aae42ffc76',
    varMargin: { currency: '', units: 0, nano: 0 },
    expectedYieldFifo: { units: 0, nano: -1******** },
    dailyYield: { currency: 'rub', units: 0, nano: 2******** }
  },
  {
    figi: 'TCS60A101X76',
    instrumentType: 'etf',
    quantity: { units: 18, nano: 0 },
    averagePositionPrice: { currency: 'rub', units: 6, nano: 740000000 },
    expectedYield: { units: 0, nano: -310000000 },
    currentNkd: undefined,
    averagePositionPricePt: { units: 0, nano: 0 },
    currentPrice: { currency: 'rub', units: 6, nano: ********* },
    averagePositionPriceFifo: { currency: 'rub', units: 6, nano: 720000000 },
    quantityLots: { units: 18, nano: 0 },
    blocked: false,
    blockedLots: { units: 0, nano: 0 },
    positionUid: 'ffd48f38-aa9b-491c-8e40-140a3f0bc931',
    instrumentUid: 'f509af83-6e71-462f-901f-bcb073f6773b',
    varMargin: { currency: '', units: 0, nano: 0 },
    expectedYieldFifo: { units: 0, nano: -310000000 },
    dailyYield: { currency: 'rub', units: 0, nano: 1******** }
  },
  {
    figi: 'TCS00A10B0G9',
    instrumentType: 'etf',
    quantity: { units: 24, nano: 0 },
    averagePositionPrice: { currency: 'rub', units: 9, nano: 960000000 },
    expectedYield: { units: -1, nano: -620000000 },
    currentNkd: undefined,
    averagePositionPricePt: { units: 0, nano: 0 },
    currentPrice: { currency: 'rub', units: 9, nano: 8******** },
    averagePositionPriceFifo: { currency: 'rub', units: 9, nano: 950000000 },
    quantityLots: { units: 24, nano: 0 },
    blocked: false,
    blockedLots: { units: 0, nano: 0 },
    positionUid: '0599cd56-02b0-4ceb-8045-eae66523979e',
    instrumentUid: 'fa20b747-e414-4922-963a-d19b752535b2',
    varMargin: { currency: '', units: 0, nano: 0 },
    expectedYieldFifo: { units: -1, nano: -620000000 },
    dailyYield: { currency: 'rub', units: 0, nano: -240000000 }
  }
]
2025-08-26T13:10:06.979Z bot:provider Getting positions
2025-08-26T13:10:07.267Z bot:provider positions {
  money: [ { currency: 'rub', units: -360, nano: -********* } ],
  blocked: [],
  securities: [
    {
      figi: 'BBG000000001',
      blocked: 0,
      balance: 14,
      positionUid: '8005e2ec-66b3-49ae-9711-a424d9c9b61b',
      instrumentUid: 'e2d0dbac-d354-4c36-a5ed-e5aae42ffc76',
      exchangeBlocked: false,
      instrumentType: 'etf'
    },
    {
      figi: 'TCS60A101X76',
      blocked: 0,
      balance: 18,
      positionUid: 'ffd48f38-aa9b-491c-8e40-140a3f0bc931',
      instrumentUid: 'f509af83-6e71-462f-901f-bcb073f6773b',
      exchangeBlocked: false,
      instrumentType: 'etf'
    },
    {
      figi: 'TCS10A107563',
      blocked: 0,
      balance: 12,
      positionUid: '8f747dc6-21bf-4f3a-878e-834880728a29',
      instrumentUid: 'd5cba263-cda7-440c-a21d-134fb5d334f6',
      exchangeBlocked: false,
      instrumentType: 'etf'
    },
    {
      figi: 'TCS00A10B0G9',
      blocked: 0,
      balance: 24,
      positionUid: '0599cd56-02b0-4ceb-8045-eae66523979e',
      instrumentUid: 'fa20b747-e414-4922-963a-d19b752535b2',
      exchangeBlocked: false,
      instrumentType: 'etf'
    },
    {
      figi: 'TCS80A101X50',
      blocked: 0,
      balance: 12,
      positionUid: '46dd8924-24e9-4581-afd2-97e0ca89009c',
      instrumentUid: 'de82be66-3b9b-4612-9572-61e3c6039013',
      exchangeBlocked: false,
      instrumentType: 'etf'
    }
  ],
  limitsLoadingInProgress: false,
  futures: [],
  options: [],
  accountId: '**********'
}
2025-08-26T13:10:07.268Z bot:provider Adding currencies to Wallet
2025-08-26T13:10:07.268Z bot:provider corePosition {
  pair: 'RUB/RUB',
  base: 'RUB',
  quote: 'RUB',
  figi: undefined,
  amount: -360,
  lotSize: 1,
  price: { units: 1, nano: 0 },
  priceNumber: 1,
  lotPrice: { units: 1, nano: 0 }
}
2025-08-26T13:10:07.269Z bot:provider Adding positions to Wallet
2025-08-26T13:10:07.269Z bot:provider position {
  figi: 'RUB000UTSTOM',
  instrumentType: 'currency',
  quantity: { units: -360, nano: -********* },
  averagePositionPrice: { currency: 'rub', units: 1, nano: 0 },
  expectedYield: { units: 0, nano: 0 },
  currentNkd: undefined,
  averagePositionPricePt: { units: 0, nano: 0 },
  currentPrice: { currency: 'rub', units: 1, nano: 0 },
  averagePositionPriceFifo: { currency: 'rub', units: 1, nano: 0 },
  quantityLots: { units: -360, nano: -********* },
  blocked: false,
  blockedLots: { units: 0, nano: 0 },
  positionUid: '33e24a92-aab0-409c-88b8-f2d57415b920',
  instrumentUid: 'a92e2e25-a698-45cc-a781-167cf465257c',
  varMargin: { currency: '', units: 0, nano: 0 },
  expectedYieldFifo: { units: 0, nano: 0 },
  dailyYield: { currency: 'rub', units: 0, nano: 0 }
}
2025-08-26T13:10:07.274Z bot:provider instrument undefined
2025-08-26T13:10:07.274Z bot:provider instrument not found by figi, skip position RUB000UTSTOM
2025-08-26T13:10:07.274Z bot:provider position {
  figi: 'TCS10A107563',
  instrumentType: 'etf',
  quantity: { units: 12, nano: 0 },
  averagePositionPrice: { currency: 'rub', units: 10, nano: 640000000 },
  expectedYield: { units: 0, nano: -720000000 },
  currentNkd: undefined,
  averagePositionPricePt: { units: 0, nano: 0 },
  currentPrice: { currency: 'rub', units: 10, nano: 550000000 },
  averagePositionPriceFifo: { currency: 'rub', units: 10, nano: 610000000 },
  quantityLots: { units: 12, nano: 0 },
  blocked: false,
  blockedLots: { units: 0, nano: 0 },
  positionUid: '8f747dc6-21bf-4f3a-878e-834880728a29',
  instrumentUid: 'd5cba263-cda7-440c-a21d-134fb5d334f6',
  varMargin: { currency: '', units: 0, nano: 0 },
  expectedYieldFifo: { units: 0, nano: -720000000 },
  dailyYield: { currency: 'rub', units: 0, nano: 240000000 }
}
2025-08-26T13:10:07.276Z bot:provider instrument {
  figi: 'TCS10A107563',
  ticker: 'TDIV@',
  classCode: 'SPBRU',
  isin: 'RU000A107563',
  lot: 1,
  currency: 'rub',
  klong: { units: 2, nano: 0 },
  kshort: { units: 2, nano: 0 },
  dlong: { units: 0, nano: 250000000 },
  dshort: { units: 0, nano: ********* },
  dlongMin: { units: 0, nano: ********* },
  dshortMin: { units: 0, nano: 350000000 },
  shortEnabledFlag: true,
  name: 'Дивидендные акции',
  exchange: 'spb_etf_ru_noweekend',
  fixedCommission: { units: 2, nano: 0 },
  focusType: 'equity',
  releasedDate: 2023-10-12T00:00:00.000Z,
  numShares: undefined,
  countryOfRisk: 'RU',
  countryOfRiskName: 'Российская Федерация',
  sector: 'other',
  rebalancingFreq: 'annual',
  tradingStatus: 5,
  otcFlag: false,
  buyAvailableFlag: true,
  sellAvailableFlag: true,
  minPriceIncrement: { units: 0, nano: 10000000 },
  apiTradeAvailableFlag: true,
  uid: 'd5cba263-cda7-440c-a21d-134fb5d334f6',
  realExchange: 2,
  positionUid: '8f747dc6-21bf-4f3a-878e-834880728a29',
  assetUid: 'a47c97b9-35b0-43f5-b4b7-05f42dfcaf67',
  instrumentExchange: 0,
  forIisFlag: true,
  forQualInvestorFlag: false,
  weekendFlag: false,
  blockedTcaFlag: false,
  liquidityFlag: true,
  first1minCandleDate: 2023-11-23T06:59:00.000Z,
  first1dayCandleDate: 2023-11-23T07:00:00.000Z,
  brand: {
    logoName: 'TDIV.png',
    logoBaseColor: '#000000',
    textColor: '#ffffff'
  },
  dlongClient: { units: 0, nano: ********* },
  dshortClient: { units: 0, nano: 350000000 }
}
2025-08-26T13:10:07.277Z bot:provider Getting last price
2025-08-26T13:10:07.522Z bot:provider lastPriceResult {
  lastPrices: [
    {
      figi: 'TCS10A107563',
      price: [Object],
      time: 2025-08-26T13:09:55.521Z,
      instrumentUid: 'd5cba263-cda7-440c-a21d-134fb5d334f6',
      lastPriceType: 1
    }
  ]
}
2025-08-26T13:10:07.522Z bot:provider lastPrice { units: 10, nano: 550000000 }
2025-08-26T13:10:12.529Z bot:provider priceWhenAddToWallet { units: 10, nano: 550000000 }
2025-08-26T13:10:12.530Z bot:provider corePosition {
  pair: 'TDIV@/RUB',
  base: 'TDIV@',
  quote: 'RUB',
  figi: 'TCS10A107563',
  amount: 12,
  lotSize: 1,
  price: { units: 10, nano: 550000000 },
  priceNumber: 10.55,
  lotPrice: { units: 10, nano: 550000000 },
  totalPrice: { units: 126, nano: ********* },
  totalPriceNumber: 126.*********00001
}
2025-08-26T13:10:12.530Z bot:provider position {
  figi: 'TCS80A101X50',
  instrumentType: 'etf',
  quantity: { units: 12, nano: 0 },
  averagePositionPrice: { currency: 'rub', units: 10, nano: 750000000 },
  expectedYield: { units: 0, nano: 3******** },
  currentNkd: undefined,
  averagePositionPricePt: { units: 0, nano: 0 },
  currentPrice: { currency: 'rub', units: 10, nano: 790000000 },
  averagePositionPriceFifo: { currency: 'rub', units: 10, nano: 760000000 },
  quantityLots: { units: 12, nano: 0 },
  blocked: false,
  blockedLots: { units: 0, nano: 0 },
  positionUid: '46dd8924-24e9-4581-afd2-97e0ca89009c',
  instrumentUid: 'de82be66-3b9b-4612-9572-61e3c6039013',
  varMargin: { currency: '', units: 0, nano: 0 },
  expectedYieldFifo: { units: 0, nano: 3******** },
  dailyYield: { currency: 'rub', units: 0, nano: 320000000 }
}
2025-08-26T13:10:12.532Z bot:provider instrument {
  figi: 'TCS80A101X50',
  ticker: 'TGLD@',
  classCode: 'SPBRU',
  isin: 'RU000A101X50',
  lot: 1,
  currency: 'rub',
  klong: { units: 2, nano: 0 },
  kshort: { units: 2, nano: 0 },
  dlong: { units: 0, nano: 181800000 },
  dshort: { units: 0, nano: ********* },
  dlongMin: { units: 0, nano: 125000000 },
  dshortMin: { units: 0, nano: 166600000 },
  shortEnabledFlag: true,
  name: 'Золото',
  exchange: 'spb_etf_t',
  fixedCommission: { units: 2, nano: 0 },
  focusType: 'alternative_investment',
  releasedDate: 2020-07-13T00:00:00.000Z,
  numShares: undefined,
  countryOfRisk: 'RU',
  countryOfRiskName: 'Российская Федерация',
  sector: '',
  rebalancingFreq: '',
  tradingStatus: 5,
  otcFlag: false,
  buyAvailableFlag: true,
  sellAvailableFlag: true,
  minPriceIncrement: { units: 0, nano: 10000000 },
  apiTradeAvailableFlag: true,
  uid: 'de82be66-3b9b-4612-9572-61e3c6039013',
  realExchange: 2,
  positionUid: '46dd8924-24e9-4581-afd2-97e0ca89009c',
  assetUid: '1ca14ff7-ab31-4657-9303-b0455a1290cb',
  instrumentExchange: 0,
  forIisFlag: true,
  forQualInvestorFlag: false,
  weekendFlag: false,
  blockedTcaFlag: false,
  liquidityFlag: true,
  first1minCandleDate: 2021-08-25T07:02:00.000Z,
  first1dayCandleDate: 2021-08-25T07:00:00.000Z,
  brand: {
    logoName: 'TGLD.png',
    logoBaseColor: '#000000',
    textColor: '#ffffff'
  },
  dlongClient: { units: 0, nano: 125000000 },
  dshortClient: { units: 0, nano: 166600000 }
}
2025-08-26T13:10:12.533Z bot:provider Getting last price
2025-08-26T13:10:12.781Z bot:provider lastPriceResult {
  lastPrices: [
    {
      figi: 'TCS80A101X50',
      price: [Object],
      time: 2025-08-26T13:10:01.193Z,
      instrumentUid: 'de82be66-3b9b-4612-9572-61e3c6039013',
      lastPriceType: 1
    }
  ]
}
2025-08-26T13:10:12.781Z bot:provider lastPrice { units: 10, nano: 790000000 }
2025-08-26T13:10:17.783Z bot:provider priceWhenAddToWallet { units: 10, nano: 790000000 }
2025-08-26T13:10:17.783Z bot:provider corePosition {
  pair: 'TGLD@/RUB',
  base: 'TGLD@',
  quote: 'RUB',
  figi: 'TCS80A101X50',
  amount: 12,
  lotSize: 1,
  price: { units: 10, nano: 790000000 },
  priceNumber: 10.79,
  lotPrice: { units: 10, nano: 790000000 },
  totalPrice: { units: 129, nano: 4******** },
  totalPriceNumber: 129.48
}
2025-08-26T13:10:17.783Z bot:provider position {
  figi: 'BBG000000001',
  instrumentType: 'etf',
  quantity: { units: 14, nano: 0 },
  averagePositionPrice: { currency: 'rub', units: 9, nano: 320000000 },
  expectedYield: { units: 0, nano: -1******** },
  currentNkd: undefined,
  averagePositionPricePt: { units: 0, nano: 0 },
  currentPrice: { currency: 'rub', units: 9, nano: 300000000 },
  averagePositionPriceFifo: { currency: 'rub', units: 9, nano: 310000000 },
  quantityLots: { units: 14, nano: 0 },
  blocked: false,
  blockedLots: { units: 0, nano: 0 },
  positionUid: '8005e2ec-66b3-49ae-9711-a424d9c9b61b',
  instrumentUid: 'e2d0dbac-d354-4c36-a5ed-e5aae42ffc76',
  varMargin: { currency: '', units: 0, nano: 0 },
  expectedYieldFifo: { units: 0, nano: -1******** },
  dailyYield: { currency: 'rub', units: 0, nano: 2******** }
}
2025-08-26T13:10:17.785Z bot:provider instrument {
  figi: 'BBG000000001',
  ticker: 'TRUR',
  classCode: 'TQTF',
  isin: 'RU000A1011U5',
  lot: 1,
  currency: 'rub',
  klong: { units: 2, nano: 0 },
  kshort: { units: 2, nano: 0 },
  dlong: { units: 0, nano: 181800000 },
  dshort: { units: 0, nano: ********* },
  dlongMin: { units: 0, nano: 125000000 },
  dshortMin: { units: 0, nano: 142800000 },
  shortEnabledFlag: true,
  name: 'Вечный портфель',
  exchange: 'moex_etf',
  fixedCommission: { units: 2, nano: 0 },
  focusType: 'mixed_allocation',
  releasedDate: 2019-11-07T00:00:00.000Z,
  numShares: undefined,
  countryOfRisk: 'RU',
  countryOfRiskName: 'Российская Федерация',
  sector: '',
  rebalancingFreq: 'annual',
  tradingStatus: 5,
  otcFlag: false,
  buyAvailableFlag: true,
  sellAvailableFlag: true,
  minPriceIncrement: { units: 0, nano: 10000000 },
  apiTradeAvailableFlag: true,
  uid: 'e2d0dbac-d354-4c36-a5ed-e5aae42ffc76',
  realExchange: 1,
  positionUid: '8005e2ec-66b3-49ae-9711-a424d9c9b61b',
  assetUid: 'df43c28e-9523-4ee2-8383-9fdb88b49e34',
  instrumentExchange: 0,
  forIisFlag: true,
  forQualInvestorFlag: false,
  weekendFlag: false,
  blockedTcaFlag: false,
  liquidityFlag: true,
  first1minCandleDate: 2019-12-09T07:11:00.000Z,
  first1dayCandleDate: 2019-12-09T07:00:00.000Z,
  brand: {
    logoName: 'TRUR.png',
    logoBaseColor: '#000000',
    textColor: '#ffffff'
  },
  dlongClient: { units: 0, nano: 125000000 },
  dshortClient: { units: 0, nano: 142800000 }
}
2025-08-26T13:10:17.786Z bot:provider Getting last price
2025-08-26T13:10:18.006Z bot:provider lastPriceResult {
  lastPrices: [
    {
      figi: 'BBG000000001',
      price: [Object],
      time: 2025-08-26T13:10:17.345Z,
      instrumentUid: 'e2d0dbac-d354-4c36-a5ed-e5aae42ffc76',
      lastPriceType: 1
    }
  ]
}
2025-08-26T13:10:18.006Z bot:provider lastPrice { units: 9, nano: 300000000 }
2025-08-26T13:10:23.007Z bot:provider priceWhenAddToWallet { units: 9, nano: 300000000 }
2025-08-26T13:10:23.007Z bot:provider corePosition {
  pair: 'TRUR/RUB',
  base: 'TRUR',
  quote: 'RUB',
  figi: 'BBG000000001',
  amount: 14,
  lotSize: 1,
  price: { units: 9, nano: 300000000 },
  priceNumber: 9.3,
  lotPrice: { units: 9, nano: 300000000 },
  totalPrice: { units: 130, nano: ********* },
  totalPriceNumber: 130.*********00002
}
2025-08-26T13:10:23.007Z bot:provider position {
  figi: 'TCS60A101X76',
  instrumentType: 'etf',
  quantity: { units: 18, nano: 0 },
  averagePositionPrice: { currency: 'rub', units: 6, nano: 740000000 },
  expectedYield: { units: 0, nano: -310000000 },
  currentNkd: undefined,
  averagePositionPricePt: { units: 0, nano: 0 },
  currentPrice: { currency: 'rub', units: 6, nano: ********* },
  averagePositionPriceFifo: { currency: 'rub', units: 6, nano: 720000000 },
  quantityLots: { units: 18, nano: 0 },
  blocked: false,
  blockedLots: { units: 0, nano: 0 },
  positionUid: 'ffd48f38-aa9b-491c-8e40-140a3f0bc931',
  instrumentUid: 'f509af83-6e71-462f-901f-bcb073f6773b',
  varMargin: { currency: '', units: 0, nano: 0 },
  expectedYieldFifo: { units: 0, nano: -310000000 },
  dailyYield: { currency: 'rub', units: 0, nano: 1******** }
}
2025-08-26T13:10:23.009Z bot:provider instrument {
  figi: 'TCS60A101X76',
  ticker: 'TMOS@',
  classCode: 'SPBRU',
  isin: 'RU000A101X76',
  lot: 1,
  currency: 'rub',
  klong: { units: 2, nano: 0 },
  kshort: { units: 2, nano: 0 },
  dlong: { units: 0, nano: 181800000 },
  dshort: { units: 0, nano: ********* },
  dlongMin: { units: 0, nano: 125000000 },
  dshortMin: { units: 0, nano: 142800000 },
  shortEnabledFlag: true,
  name: 'Крупнейшие компании РФ',
  exchange: 'spb_etf_ru_noweekend',
  fixedCommission: { units: 0, nano: ********* },
  focusType: 'equity',
  releasedDate: 2020-07-13T00:00:00.000Z,
  numShares: undefined,
  countryOfRisk: 'RU',
  countryOfRiskName: 'Российская Федерация',
  sector: 'other',
  rebalancingFreq: 'quarterly',
  tradingStatus: 5,
  otcFlag: false,
  buyAvailableFlag: true,
  sellAvailableFlag: true,
  minPriceIncrement: { units: 0, nano: 10000000 },
  apiTradeAvailableFlag: true,
  uid: 'f509af83-6e71-462f-901f-bcb073f6773b',
  realExchange: 2,
  positionUid: 'ffd48f38-aa9b-491c-8e40-140a3f0bc931',
  assetUid: '41ee95a0-318f-49e1-bfe4-2c3f7c83fe21',
  instrumentExchange: 0,
  forIisFlag: true,
  forQualInvestorFlag: false,
  weekendFlag: false,
  blockedTcaFlag: false,
  liquidityFlag: true,
  first1minCandleDate: 2020-08-26T07:14:00.000Z,
  first1dayCandleDate: 2020-08-26T07:00:00.000Z,
  brand: {
    logoName: 'TMOS.png',
    logoBaseColor: '#000000',
    textColor: '#ffffff'
  },
  dlongClient: { units: 0, nano: 125000000 },
  dshortClient: { units: 0, nano: 142800000 }
}
2025-08-26T13:10:23.009Z bot:provider Getting last price
2025-08-26T13:10:23.233Z bot:provider lastPriceResult {
  lastPrices: [
    {
      figi: 'TCS60A101X76',
      price: [Object],
      time: 2025-08-26T13:10:19.728Z,
      instrumentUid: 'f509af83-6e71-462f-901f-bcb073f6773b',
      lastPriceType: 1
    }
  ]
}
2025-08-26T13:10:23.234Z bot:provider lastPrice { units: 6, nano: ********* }
2025-08-26T13:10:28.235Z bot:provider priceWhenAddToWallet { units: 6, nano: ********* }
2025-08-26T13:10:28.236Z bot:provider corePosition {
  pair: 'TMOS@/RUB',
  base: 'TMOS@',
  quote: 'RUB',
  figi: 'TCS60A101X76',
  amount: 18,
  lotSize: 1,
  price: { units: 6, nano: ********* },
  priceNumber: 6.7,
  lotPrice: { units: 6, nano: ********* },
  totalPrice: { units: 120, nano: ********* },
  totalPriceNumber: 120.*********00001
}
2025-08-26T13:10:28.236Z bot:provider position {
  figi: 'TCS00A10B0G9',
  instrumentType: 'etf',
  quantity: { units: 24, nano: 0 },
  averagePositionPrice: { currency: 'rub', units: 9, nano: 960000000 },
  expectedYield: { units: -1, nano: -620000000 },
  currentNkd: undefined,
  averagePositionPricePt: { units: 0, nano: 0 },
  currentPrice: { currency: 'rub', units: 9, nano: 8******** },
  averagePositionPriceFifo: { currency: 'rub', units: 9, nano: 950000000 },
  quantityLots: { units: 24, nano: 0 },
  blocked: false,
  blockedLots: { units: 0, nano: 0 },
  positionUid: '0599cd56-02b0-4ceb-8045-eae66523979e',
  instrumentUid: 'fa20b747-e414-4922-963a-d19b752535b2',
  varMargin: { currency: '', units: 0, nano: 0 },
  expectedYieldFifo: { units: -1, nano: -620000000 },
  dailyYield: { currency: 'rub', units: 0, nano: -240000000 }
}
2025-08-26T13:10:28.238Z bot:provider instrument {
  figi: 'TCS00A10B0G9',
  ticker: 'TRND',
  classCode: 'TQTF',
  isin: 'RU000A10B0G9',
  lot: 1,
  currency: 'rub',
  klong: { units: 2, nano: 0 },
  kshort: { units: 2, nano: 0 },
  dlong: { units: 0, nano: 350000000 },
  dshort: undefined,
  dlongMin: { units: 0, nano: 300000000 },
  dshortMin: undefined,
  shortEnabledFlag: false,
  name: 'Трендовые акции',
  exchange: 'MOEX_PLUS',
  fixedCommission: { units: 2, nano: 0 },
  focusType: 'equity',
  releasedDate: 2025-02-20T00:00:00.000Z,
  numShares: undefined,
  countryOfRisk: 'RU',
  countryOfRiskName: 'Российская Федерация',
  sector: 'other',
  rebalancingFreq: 'quarterly',
  tradingStatus: 5,
  otcFlag: false,
  buyAvailableFlag: true,
  sellAvailableFlag: true,
  minPriceIncrement: { units: 0, nano: 10000000 },
  apiTradeAvailableFlag: true,
  uid: 'fa20b747-e414-4922-963a-d19b752535b2',
  realExchange: 1,
  positionUid: '0599cd56-02b0-4ceb-8045-eae66523979e',
  assetUid: '63334cd2-9656-41f0-9889-8ef28b89e56e',
  instrumentExchange: 0,
  forIisFlag: true,
  forQualInvestorFlag: false,
  weekendFlag: false,
  blockedTcaFlag: false,
  liquidityFlag: true,
  first1minCandleDate: 2025-03-24T07:00:00.000Z,
  first1dayCandleDate: 2025-03-24T00:00:00.000Z,
  brand: {
    logoName: 'TRND.png',
    logoBaseColor: '#000000',
    textColor: '#ffffff'
  },
  dlongClient: { units: 0, nano: 300000000 },
  dshortClient: { units: 0, nano: 0 }
}
2025-08-26T13:10:28.238Z bot:provider Getting last price
2025-08-26T13:10:28.464Z bot:provider lastPriceResult {
  lastPrices: [
    {
      figi: 'TCS00A10B0G9',
      price: [Object],
      time: 2025-08-26T13:10:19.607Z,
      instrumentUid: 'fa20b747-e414-4922-963a-d19b752535b2',
      lastPriceType: 1
    }
  ]
}
2025-08-26T13:10:28.464Z bot:provider lastPrice { units: 9, nano: 890000000 }
2025-08-26T13:10:33.466Z bot:provider priceWhenAddToWallet { units: 9, nano: 890000000 }
2025-08-26T13:10:33.467Z bot:provider corePosition {
  pair: 'TRND/RUB',
  base: 'TRND',
  quote: 'RUB',
  figi: 'TCS00A10B0G9',
  amount: 24,
  lotSize: 1,
  price: { units: 9, nano: 890000000 },
  priceNumber: 9.88,
  lotPrice: { units: 9, nano: 890000000 },
  totalPrice: { units: 237, nano: 120000000 },
  totalPriceNumber: 237.12
}
2025-08-26T13:10:33.468Z bot:provider [
  {
    pair: 'RUB/RUB',
    base: 'RUB',
    quote: 'RUB',
    figi: undefined,
    amount: -360,
    lotSize: 1,
    price: { units: 1, nano: 0 },
    priceNumber: 1,
    lotPrice: { units: 1, nano: 0 }
  },
  {
    pair: 'TDIV@/RUB',
    base: 'TDIV@',
    quote: 'RUB',
    figi: 'TCS10A107563',
    amount: 12,
    lotSize: 1,
    price: { units: 10, nano: 550000000 },
    priceNumber: 10.55,
    lotPrice: { units: 10, nano: 550000000 },
    totalPrice: { units: 126, nano: ********* },
    totalPriceNumber: 126.*********00001
  },
  {
    pair: 'TGLD@/RUB',
    base: 'TGLD@',
    quote: 'RUB',
    figi: 'TCS80A101X50',
    amount: 12,
    lotSize: 1,
    price: { units: 10, nano: 790000000 },
    priceNumber: 10.79,
    lotPrice: { units: 10, nano: 790000000 },
    totalPrice: { units: 129, nano: 4******** },
    totalPriceNumber: 129.48
  },
  {
    pair: 'TRUR/RUB',
    base: 'TRUR',
    quote: 'RUB',
    figi: 'BBG000000001',
    amount: 14,
    lotSize: 1,
    price: { units: 9, nano: 300000000 },
    priceNumber: 9.3,
    lotPrice: { units: 9, nano: 300000000 },
    totalPrice: { units: 130, nano: ********* },
    totalPriceNumber: 130.*********00002
  },
  {
    pair: 'TMOS@/RUB',
    base: 'TMOS@',
    quote: 'RUB',
    figi: 'TCS60A101X76',
    amount: 18,
    lotSize: 1,
    price: { units: 6, nano: ********* },
    priceNumber: 6.7,
    lotPrice: { units: 6, nano: ********* },
    totalPrice: { units: 120, nano: ********* },
    totalPriceNumber: 120.*********00001
  },
  {
    pair: 'TRND/RUB',
    base: 'TRND',
    quote: 'RUB',
    figi: 'TCS00A10B0G9',
    amount: 24,
    lotSize: 1,
    price: { units: 9, nano: 890000000 },
    priceNumber: 9.88,
    lotPrice: { units: 9, nano: 890000000 },
    totalPrice: { units: 237, nano: 120000000 },
    totalPriceNumber: 237.12
  }
]
[etfCap] buildAumMapSmart: fetched HTML length=241510
[etfCap] buildAumMapSmart: auto result: {}
[etfCap] [DEBUG] TBRU AUM search: FOUND
[etfCap] [DEBUG] TDIV AUM search: FOUND
[etfCap] [DEBUG] TITR AUM search: FOUND
[etfCap] [DEBUG] TMON AUM search: FOUND
[etfCap] [DEBUG] TMOS AUM search: FOUND
[etfCap] [DEBUG] TOFZ AUM search: FOUND
[etfCap] buildAumMapSmart: final result: {
  TPAY: {
    amount: 22840535834.17,
    currency: "RUB",
  },
  TGLD: {
    amount: 113027064.99,
    currency: "USD",
  },
  TRUR: {
    amount: 16944126813.56,
    currency: "RUB",
  },
  TRND: {
    amount: *********.84,
    currency: "RUB",
  },
  TBRU: {
    amount: 5055918283.76,
    currency: "RUB",
  },
  TDIV: {
    amount: 1009706095.75,
    currency: "RUB",
  },
  TITR: {
    amount: 451284431.12,
    currency: "RUB",
  },
  TLCB: {
    amount: 10915848062.94,
    currency: "RUB",
  },
  TMON: {
    amount: 205183053216,
    currency: "RUB",
  },
  TMOS: {
    amount: 12288910124.98,
    currency: "RUB",
  },
  TOFZ: {
    amount: 3654379925.22,
    currency: "RUB",
  },
}
[pollEtfMetrics] symbol=TPAY search sharesCount via Smartfeed API
[pollEtfMetrics] smartfeed brand=Пассивный доход news=50 cursorNext=ts:1753963176077…
[pollEtfMetrics] smartfeed brand=Пассивный доход news=50 cursorNext=ts:1751289429669…
[pollEtfMetrics] smartfeed brand=Пассивный доход news=50 cursorNext=ts:1748465649591…
[pollEtfMetrics] smartfeed hit id=18548236 title=Количество паев в обращении уменьшилось count=225600000
[pollEtfMetrics] price: lastPriceRUB=101.69 for TPAY
[pollEtfMetrics] payload TPAY: price=101.69 shares=225600000 aum=22840535834.17 mcap=22941264000
[pollEtfMetrics] saved /Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/etf_metrics/TPAY.json
[pollEtfMetrics] symbol=TGLD search sharesCount via Smartfeed API
[pollEtfMetrics] smartfeed brand=Золото news=50 cursorNext=ts:1754497092000…
[pollEtfMetrics] smartfeed brand=Золото news=50 cursorNext=ts:1753341720000…
[pollEtfMetrics] smartfeed hit id=18548894 title=Количество паев в обращении уменьшилось count=843600000
[pollEtfMetrics] price: lastPriceRUB=10.8 for TGLD
[pollEtfMetrics] payload TGLD: price=10.8 shares=843600000 aum=9119498317.066158 mcap=9110880000
[pollEtfMetrics] saved /Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/etf_metrics/TGLD.json
[pollEtfMetrics] symbol=TRUR search sharesCount via Smartfeed API
[pollEtfMetrics] smartfeed brand=Вечный портфель news=50 cursorNext=ts:1748984045726…
[pollEtfMetrics] smartfeed hit id=18549024 title=В фонд поступили новые деньги count=1818500000
[pollEtfMetrics] price: lastPriceRUB=9.3 for TRUR
[pollEtfMetrics] payload TRUR: price=9.3 shares=1818500000 aum=16944126813.56 mcap=16912050000.000002
[pollEtfMetrics] saved /Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/etf_metrics/TRUR.json
[pollEtfMetrics] symbol=TRND search sharesCount via Smartfeed API
[pollEtfMetrics] smartfeed brand=Трендовые акции news=0 cursorNext=null
[pollEtfMetrics] symbol=TRND sharesCount from local cache: ********
[pollEtfMetrics] price: lastPriceRUB=9.88 for TRND
[pollEtfMetrics] payload TRND: price=9.88 shares=******** aum=*********.84 mcap=*********.********
[pollEtfMetrics] saved /Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/etf_metrics/TRND.json
[pollEtfMetrics] symbol=TBRU search sharesCount via Smartfeed API
[pollEtfMetrics] smartfeed brand=Российские облигации news=50 cursorNext=ts:*************…
[pollEtfMetrics] [DEBUG] Parsing API: https://www.tbank.ru/api/invest/smartfeed-public/v1/feed/api/brands/%D0%A0%D0%BE%D1%81%D1%81%D0%B8%D0%B9%D1%81%D0%BA%D0%B8%D0%B5%20%D0%BE%D0%B1%D0%BB%D0%B8%D0%B3%D0%B0%D1%86%D0%B8%D0%B8/fund-news?limit=50
[pollEtfMetrics] [DEBUG] Found 50 news items
[pollEtfMetrics] [DEBUG] News 1: id=********, title="В фонд поступили новые деньги"
[pollEtfMetrics] [DEBUG] Additional fields: [{"name":"Дата выдачи паев","value":"26.08.2025"},{"name":"Выпущено паев","value":"4,7 млн шт."},{"name":"На сумму","value":"35 млн ₽"},{"name":"Всего паев","value":"702,1 млн шт."},{"name":"Цена пая","value":"7,3839 ₽"}]
[pollEtfMetrics] [DEBUG] News 2: id=********, title="Фонд получил купон по облигациям Гидромашсервис 001Р-04"
[pollEtfMetrics] [DEBUG] Additional fields: [{"name":"Тип дохода","value":"Купон"},{"name":"Сумма дохода","value":"1,3 млн ₽"},{"name":"Доля фонда","value":"0,03 %"}]
[pollEtfMetrics] [DEBUG] News 3: id=18549099, title="Фонд получил купон по облигациям Софтлайн выпуск 002Р-01"
[pollEtfMetrics] [DEBUG] Additional fields: [{"name":"Тип дохода","value":"Купон"},{"name":"Сумма дохода","value":"868,5 тыс ₽"},{"name":"Доля фонда","value":"0,02 %"}]
[pollEtfMetrics] [DEBUG] News 4: id=18549096, title="Совершены сделки с ценными бумагами в составе фонда"
[pollEtfMetrics] [DEBUG] News 5: id=18549090, title="В фонд поступили новые деньги"
[pollEtfMetrics] [DEBUG] Additional fields: [{"name":"Дата выдачи паев","value":"25.08.2025"},{"name":"Выпущено паев","value":"12,2 млн шт."},{"name":"На сумму","value":"90 млн ₽"},{"name":"Всего паев","value":"697,4 млн шт."},{"name":"Цена пая","value":"7,3787 ₽"}]
[pollEtfMetrics] smartfeed hit id=******** title=В фонд поступили новые деньги count=702100000
[pollEtfMetrics] [DEBUG] AUM search for TBRU:
[pollEtfMetrics] [DEBUG] AUM map entry: {"amount":5055918283.76,"currency":"RUB"}
[pollEtfMetrics] [DEBUG] AUM result: found
[pollEtfMetrics] price: lastPriceRUB=7.4 for TBRU
[pollEtfMetrics] payload TBRU: price=7.4 shares=702100000 aum=5055918283.76 mcap=5195540000
[pollEtfMetrics] saved /Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/etf_metrics/TBRU.json
[pollEtfMetrics] symbol=TDIV search sharesCount via Smartfeed API
[pollEtfMetrics] smartfeed brand=Дивидендные акции news=50 cursorNext=ts:1753365997104…
[pollEtfMetrics] smartfeed hit id=18548929 title=Количество паев в обращении уменьшилось count=95500000
[pollEtfMetrics] price: lastPriceRUB=10.55 for TDIV
[pollEtfMetrics] payload TDIV: price=10.55 shares=95500000 aum=1009706095.75 mcap=1007525000.0000001
[pollEtfMetrics] saved /Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/etf_metrics/TDIV.json
[pollEtfMetrics] symbol=TITR search sharesCount via Smartfeed API
[pollEtfMetrics] smartfeed brand=Российские Технологии news=50 cursorNext=ts:1749213928249…
[pollEtfMetrics] smartfeed hit id=18549061 title=Количество паев в обращении уменьшилось count=66900000
[pollEtfMetrics] price: lastPriceRUB=6.66 for TITR
[pollEtfMetrics] payload TITR: price=6.66 shares=66900000 aum=451284431.12 mcap=445554000
[pollEtfMetrics] saved /Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/etf_metrics/TITR.json
[pollEtfMetrics] symbol=TLCB search sharesCount via Smartfeed API
[pollEtfMetrics] smartfeed brand=Локальные валютные облигации news=50 cursorNext=ts:1753876700507…
[pollEtfMetrics] smartfeed hit id=18549078 title=В фонд поступили новые деньги count=1079400000
[pollEtfMetrics] price: lastPriceRUB=10.15 for TLCB
[pollEtfMetrics] payload TLCB: price=10.15 shares=1079400000 aum=10915848062.94 mcap=10955910000
[pollEtfMetrics] saved /Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/etf_metrics/TLCB.json
[pollEtfMetrics] symbol=TMON search sharesCount via Smartfeed API
[pollEtfMetrics] smartfeed brand=Денежный рынок news=50 cursorNext=ts:1750774986870…
[pollEtfMetrics] smartfeed hit id=18549094 title=В фонд поступили новые деньги count=1467400000
[pollEtfMetrics] price: lastPriceRUB=141.71 for TMON
[pollEtfMetrics] payload TMON: price=141.71 shares=1467400000 aum=205183053216 mcap=207945254000
[pollEtfMetrics] saved /Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/etf_metrics/TMON.json
[pollEtfMetrics] symbol=TMOS search sharesCount via Smartfeed API
[pollEtfMetrics] smartfeed brand=Крупнейшие компании РФ news=50 cursorNext=ts:1752008050878…
[pollEtfMetrics] smartfeed hit id=18549104 title=В фонд поступили новые деньги count=1844300000
[pollEtfMetrics] price: lastPriceRUB=6.7 for TMOS
[pollEtfMetrics] payload TMOS: price=6.7 shares=1844300000 aum=12288910124.98 mcap=12356810000
[pollEtfMetrics] saved /Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/etf_metrics/TMOS.json
[pollEtfMetrics] symbol=TOFZ search sharesCount via Smartfeed API
[pollEtfMetrics] smartfeed brand=Государственные облигации news=50 cursorNext=ts:1752846322262…
[pollEtfMetrics] smartfeed hit id=18549093 title=В фонд поступили новые деньги count=305800000
[pollEtfMetrics] price: lastPriceRUB=13.28 for TOFZ
[pollEtfMetrics] payload TOFZ: price=13.28 shares=305800000 aum=3654379925.22 mcap=4061024000
[pollEtfMetrics] saved /Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/etf_metrics/TOFZ.json
[pollEtfMetrics] symbol=TPAY search sharesCount via Smartfeed API
[pollEtfMetrics] smartfeed brand=Пассивный доход news=50 cursorNext=ts:1753963176077…
[pollEtfMetrics] smartfeed brand=Пассивный доход news=50 cursorNext=ts:1751289429669…
[pollEtfMetrics] smartfeed brand=Пассивный доход news=50 cursorNext=ts:1748465649591…
[pollEtfMetrics] smartfeed hit id=18548236 title=Количество паев в обращении уменьшилось count=225600000
[pollEtfMetrics] price: lastPriceRUB=101.69 for TPAY
[pollEtfMetrics] payload TPAY: price=101.69 shares=225600000 aum=22840535834.17 mcap=22941264000
[pollEtfMetrics] saved /Users/<USER>/projects/tinkoff-invest-etf-balancer-bot/etf_metrics/TPAY.json
2025-08-26T13:11:15.812Z bot:balancer Margin strategy: {
  shouldRemoveMargin: false,
  reason: 'Not time to apply margin strategy',
  transferCost: 0,
  timeInfo: { timeToClose: 0, timeToNextBalance: 0, isLastBalance: false },
  marginPositions: [
    {
      pair: 'TDIV@/RUB',
      base: 'TDIV@',
      quote: 'RUB',
      figi: 'TCS10A107563',
      amount: 12,
      lotSize: 1,
      price: [Object],
      priceNumber: 10.55,
      lotPrice: [Object],
      totalPrice: [Object],
      totalPriceNumber: 126.*********00001,
      isMargin: true,
      marginValue: 94.95,
      leverage: 4,
      marginCall: false
    },
    {
      pair: 'TGLD@/RUB',
      base: 'TGLD@',
      quote: 'RUB',
      figi: 'TCS80A101X50',
      amount: 12,
      lotSize: 1,
      price: [Object],
      priceNumber: 10.79,
      lotPrice: [Object],
      totalPrice: [Object],
      totalPriceNumber: 129.48,
      isMargin: true,
      marginValue: 97.10999999999999,
      leverage: 4,
      marginCall: false
    },
    {
      pair: 'TRUR/RUB',
      base: 'TRUR',
      quote: 'RUB',
      figi: 'BBG000000001',
      amount: 14,
      lotSize: 1,
      price: [Object],
      priceNumber: 9.3,
      lotPrice: [Object],
      totalPrice: [Object],
      totalPriceNumber: 130.*********00002,
      isMargin: true,
      marginValue: 97.65,
      leverage: 4,
      marginCall: false
    },
    {
      pair: 'TMOS@/RUB',
      base: 'TMOS@',
      quote: 'RUB',
      figi: 'TCS60A101X76',
      amount: 18,
      lotSize: 1,
      price: [Object],
      priceNumber: 6.7,
      lotPrice: [Object],
      totalPrice: [Object],
      totalPriceNumber: 120.*********00001,
      isMargin: true,
      marginValue: 90.45,
      leverage: 4,
      marginCall: false
    },
    {
      pair: 'TRND/RUB',
      base: 'TRND',
      quote: 'RUB',
      figi: 'TCS00A10B0G9',
      amount: 24,
      lotSize: 1,
      price: [Object],
      priceNumber: 9.88,
      lotPrice: [Object],
      totalPrice: [Object],
      totalPriceNumber: 237.12,
      isMargin: true,
      marginValue: 177.84,
      leverage: 4,
      marginCall: false
    }
  ]
}
2025-08-26T13:11:15.813Z bot:balancer Normalizing percentages to make total sum equal 100%, to exclude human factor
2025-08-26T13:11:15.813Z bot:balancer desiredWallet {
  TRAY: 9.146257888348398,
  TGLD: 9.604582160801673,
  TRUR: 9.685722415942017,
  TRND: 17.45226985998578,
  TBRU: 7.160187107532898,
  TDIV: 9.708577073760956,
  TITR: 10.61047988975208,
  TLCB: 9.209591030884992,
  TMON: 8.371523696878718,
  TMOS: 9.050808876112496,
  TOFZ: 0,
  TPAY: 9.146257888348398
}
2025-08-26T13:11:15.814Z bot:balancer desiredSum 109.14625788834842
2025-08-26T13:11:15.814Z bot:balancer normalizedDesire {
  TRAY: 8.379818113145571,
  TGLD: 8.799735645198863,
  TRUR: 8.874076494541905,
  TRND: 15.989801389103645,
  TBRU: 6.560176451360741,
  TDIV: 8.895015973605236,
  TITR: 9.721340974058965,
  TLCB: 8.43784405353226,
  TMON: 7.6700052377814005,
  TMOS: 8.292367554525832,
  TOFZ: 0,
  TPAY: 8.379818113145571
}
2025-08-26T13:11:15.814Z bot:balancer Adding missing instruments from portfolio to DesireWallet with value 0
2025-08-26T13:11:15.814Z bot:balancer RUB not found in desired portfolio, adding with value 0.
2025-08-26T13:11:15.814Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:11:15.814Z bot:balancer positionIndex -1
2025-08-26T13:11:15.814Z bot:balancer Ticker from DesireWallet not found in portfolio. Creating.
2025-08-26T13:11:15.818Z bot:balancer {
  figi: 'TCS00A108WX3',
  ticker: 'TPAY',
  classCode: 'TQTF',
  isin: 'RU000A108WX3',
  lot: 1,
  currency: 'rub',
  klong: { units: 2, nano: 0 },
  kshort: { units: 2, nano: 0 },
  dlong: { units: 0, nano: 166600000 },
  dshort: undefined,
  dlongMin: { units: 0, nano: 153800000 },
  dshortMin: undefined,
  shortEnabledFlag: false,
  name: 'Пассивный доход',
  exchange: 'moex_etf_evening',
  fixedCommission: { units: 0, nano: 900000000 },
  focusType: 'fixed_income',
  releasedDate: 2024-06-27T00:00:00.000Z,
  numShares: undefined,
  countryOfRisk: 'RU',
  countryOfRiskName: 'Российская Федерация',
  sector: 'other',
  rebalancingFreq: 'semi_annual',
  tradingStatus: 5,
  otcFlag: false,
  buyAvailableFlag: true,
  sellAvailableFlag: true,
  minPriceIncrement: { units: 0, nano: 10000000 },
  apiTradeAvailableFlag: true,
  uid: '1d0e01e5-148c-40e5-bb8f-1bf2d8e03c1a',
  realExchange: 1,
  positionUid: '41be4f89-9d19-4eac-9ae4-09276f85956a',
  assetUid: 'a9f74b0a-f3dc-4a25-aebe-7fb1732dd510',
  instrumentExchange: 0,
  forIisFlag: true,
  forQualInvestorFlag: false,
  weekendFlag: false,
  blockedTcaFlag: false,
  liquidityFlag: true,
  first1minCandleDate: 2024-08-12T06:59:00.000Z,
  first1dayCandleDate: 2024-08-12T00:00:00.000Z,
  brand: {
    logoName: 'TPAY.png',
    logoBaseColor: '#000000',
    textColor: '#ffffff'
  },
  dlongClient: { units: 0, nano: 153800000 },
  dshortClient: { units: 0, nano: 0 }
}
2025-08-26T13:11:15.818Z bot:balancer TCS00A108WX3
2025-08-26T13:11:15.818Z bot:balancer 1
2025-08-26T13:11:15.818Z bot:provider Getting last price
2025-08-26T13:11:16.056Z bot:provider lastPriceResult {
  lastPrices: [
    {
      figi: 'TCS00A108WX3',
      price: [Object],
      time: 2025-08-26T13:11:13.802Z,
      instrumentUid: '1d0e01e5-148c-40e5-bb8f-1bf2d8e03c1a',
      lastPriceType: 1
    }
  ]
}
2025-08-26T13:11:16.057Z bot:provider lastPrice { units: 101, nano: 690000000 }
2025-08-26T13:11:21.058Z bot:balancer newPosition {
  pair: 'TPAY/RUB',
  base: 'TPAY',
  quote: 'RUB',
  figi: 'TCS00A108WX3',
  price: { units: 101, nano: 690000000 },
  priceNumber: 101.69,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 101, nano: 690000000 }
}
2025-08-26T13:11:21.058Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:11:21.059Z bot:balancer positionIndex 2
2025-08-26T13:11:21.059Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:11:21.059Z bot:balancer positionIndex 3
2025-08-26T13:11:21.059Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:11:21.059Z bot:balancer positionIndex 5
2025-08-26T13:11:21.059Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:11:21.059Z bot:balancer positionIndex -1
2025-08-26T13:11:21.059Z bot:balancer Ticker from DesireWallet not found in portfolio. Creating.
2025-08-26T13:11:21.062Z bot:balancer {
  figi: 'TCS60A1039N1',
  ticker: 'TBRU@',
  classCode: 'SPBRU',
  isin: 'RU000A1039N1',
  lot: 1,
  currency: 'rub',
  klong: { units: 2, nano: 0 },
  kshort: { units: 2, nano: 0 },
  dlong: { units: 0, nano: ********* },
  dshort: { units: 0, nano: ********* },
  dlongMin: { units: 0, nano: 142800000 },
  dshortMin: { units: 0, nano: 142800000 },
  shortEnabledFlag: true,
  name: 'Российские облигации',
  exchange: 'spb_etf_ru_noweekend',
  fixedCommission: { units: 1, nano: 500000000 },
  focusType: 'fixed_income',
  releasedDate: 2021-06-10T00:00:00.000Z,
  numShares: undefined,
  countryOfRisk: 'RU',
  countryOfRiskName: 'Российская Федерация',
  sector: 'other',
  rebalancingFreq: 'semi_annual',
  tradingStatus: 5,
  otcFlag: false,
  buyAvailableFlag: true,
  sellAvailableFlag: true,
  minPriceIncrement: { units: 0, nano: 10000000 },
  apiTradeAvailableFlag: true,
  uid: 'e8acd2fb-6de6-4ea4-9bfb-0daad9b2ed7b',
  realExchange: 2,
  positionUid: '68f44dc7-7ad2-4246-b1ba-c039b7703371',
  assetUid: 'a327ef84-2aaa-4ffe-b71c-1cbeb9dadd0f',
  instrumentExchange: 0,
  forIisFlag: true,
  forQualInvestorFlag: false,
  weekendFlag: false,
  blockedTcaFlag: false,
  liquidityFlag: true,
  first1minCandleDate: 2021-07-12T07:00:00.000Z,
  first1dayCandleDate: 2021-07-12T07:00:00.000Z,
  brand: {
    logoName: 'TBRU.png',
    logoBaseColor: '#000000',
    textColor: '#ffffff'
  },
  dlongClient: { units: 0, nano: 142800000 },
  dshortClient: { units: 0, nano: 142800000 }
}
2025-08-26T13:11:21.062Z bot:balancer TCS60A1039N1
2025-08-26T13:11:21.062Z bot:balancer 1
2025-08-26T13:11:21.062Z bot:provider Getting last price
2025-08-26T13:11:21.299Z bot:provider lastPriceResult {
  lastPrices: [
    {
      figi: 'TCS60A1039N1',
      price: [Object],
      time: 2025-08-26T13:10:01.733Z,
      instrumentUid: 'e8acd2fb-6de6-4ea4-9bfb-0daad9b2ed7b',
      lastPriceType: 1
    }
  ]
}
2025-08-26T13:11:21.300Z bot:provider lastPrice { units: 7, nano: 400000000 }
2025-08-26T13:11:26.301Z bot:balancer newPosition {
  pair: 'TBRU/RUB',
  base: 'TBRU',
  quote: 'RUB',
  figi: 'TCS60A1039N1',
  price: { units: 7, nano: 400000000 },
  priceNumber: 7.4,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 7, nano: 400000000 }
}
2025-08-26T13:11:26.301Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:11:26.301Z bot:balancer positionIndex 1
2025-08-26T13:11:26.301Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:11:26.301Z bot:balancer positionIndex -1
2025-08-26T13:11:26.301Z bot:balancer Ticker from DesireWallet not found in portfolio. Creating.
2025-08-26T13:11:26.303Z bot:balancer {
  figi: 'TCS30A108BL2',
  ticker: 'TITR@',
  classCode: 'SPBRU',
  isin: 'RU000A108BL2',
  lot: 1,
  currency: 'rub',
  klong: { units: 2, nano: 0 },
  kshort: { units: 2, nano: 0 },
  dlong: { units: 0, nano: 250000000 },
  dshort: undefined,
  dlongMin: { units: 0, nano: ********* },
  dshortMin: undefined,
  shortEnabledFlag: false,
  name: 'Российские Технологии',
  exchange: 'spb_etf_t',
  fixedCommission: { units: 2, nano: 0 },
  focusType: 'mixed_allocation',
  releasedDate: 2024-04-18T00:00:00.000Z,
  numShares: undefined,
  countryOfRisk: 'RU',
  countryOfRiskName: 'Российская Федерация',
  sector: 'other',
  rebalancingFreq: 'quarterly',
  tradingStatus: 5,
  otcFlag: false,
  buyAvailableFlag: true,
  sellAvailableFlag: true,
  minPriceIncrement: { units: 0, nano: 10000000 },
  apiTradeAvailableFlag: true,
  uid: '*************-4254-9c78-255b0067fcbe',
  realExchange: 2,
  positionUid: '09a59e18-5be2-4eec-b149-aae369f49561',
  assetUid: 'a0c7e33e-e836-46bd-928b-2486852496d5',
  instrumentExchange: 0,
  forIisFlag: true,
  forQualInvestorFlag: false,
  weekendFlag: false,
  blockedTcaFlag: false,
  liquidityFlag: true,
  first1minCandleDate: 2024-05-20T07:06:00.000Z,
  first1dayCandleDate: 2024-05-20T00:00:00.000Z,
  brand: {
    logoName: 'TITR.png',
    logoBaseColor: '#000000',
    textColor: '#ffffff'
  },
  dlongClient: { units: 0, nano: ********* },
  dshortClient: { units: 0, nano: 0 }
}
2025-08-26T13:11:26.304Z bot:balancer TCS30A108BL2
2025-08-26T13:11:26.304Z bot:balancer 1
2025-08-26T13:11:26.304Z bot:provider Getting last price
2025-08-26T13:11:26.535Z bot:provider lastPriceResult {
  lastPrices: [
    {
      figi: 'TCS30A108BL2',
      price: [Object],
      time: 2025-08-26T13:10:28.005Z,
      instrumentUid: '*************-4254-9c78-255b0067fcbe',
      lastPriceType: 1
    }
  ]
}
2025-08-26T13:11:26.535Z bot:provider lastPrice { units: 6, nano: 660000000 }
2025-08-26T13:11:31.537Z bot:balancer newPosition {
  pair: 'TITR/RUB',
  base: 'TITR',
  quote: 'RUB',
  figi: 'TCS30A108BL2',
  price: { units: 6, nano: 660000000 },
  priceNumber: 6.66,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 6, nano: 660000000 }
}
2025-08-26T13:11:31.537Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:11:31.537Z bot:balancer positionIndex -1
2025-08-26T13:11:31.537Z bot:balancer Ticker from DesireWallet not found in portfolio. Creating.
2025-08-26T13:11:31.539Z bot:balancer {
  figi: 'TCS20A107597',
  ticker: 'TLCB@',
  classCode: 'SPBRU',
  isin: 'RU000A107597',
  lot: 1,
  currency: 'rub',
  klong: { units: 2, nano: 0 },
  kshort: { units: 2, nano: 0 },
  dlong: { units: 0, nano: 250000000 },
  dshort: { units: 0, nano: ********* },
  dlongMin: { units: 0, nano: ********* },
  dshortMin: { units: 0, nano: 350000000 },
  shortEnabledFlag: true,
  name: 'Локальные валютные облигации',
  exchange: 'spb_etf_t',
  fixedCommission: { units: 1, nano: 500000000 },
  focusType: 'fixed_income',
  releasedDate: 2023-10-19T00:00:00.000Z,
  numShares: undefined,
  countryOfRisk: 'RU',
  countryOfRiskName: 'Российская Федерация',
  sector: 'other',
  rebalancingFreq: 'semi_annual',
  tradingStatus: 5,
  otcFlag: false,
  buyAvailableFlag: true,
  sellAvailableFlag: true,
  minPriceIncrement: { units: 0, nano: 10000000 },
  apiTradeAvailableFlag: true,
  uid: '2f243f46-34ce-4d50-a931-c6f8a67eb758',
  realExchange: 2,
  positionUid: '5b56d3c6-c302-4417-8c0c-a7e8e5881617',
  assetUid: '9b267b9e-d257-4b01-ad84-aac93de0b3f2',
  instrumentExchange: 0,
  forIisFlag: true,
  forQualInvestorFlag: false,
  weekendFlag: false,
  blockedTcaFlag: false,
  liquidityFlag: true,
  first1minCandleDate: 2023-11-23T07:25:00.000Z,
  first1dayCandleDate: 2023-11-23T07:00:00.000Z,
  brand: {
    logoName: 'TLCB.png',
    logoBaseColor: '#000000',
    textColor: '#ffffff'
  },
  dlongClient: { units: 0, nano: ********* },
  dshortClient: { units: 0, nano: 350000000 }
}
2025-08-26T13:11:31.539Z bot:balancer TCS20A107597
2025-08-26T13:11:31.539Z bot:balancer 1
2025-08-26T13:11:31.539Z bot:provider Getting last price
2025-08-26T13:11:31.768Z bot:provider lastPriceResult {
  lastPrices: [
    {
      figi: 'TCS20A107597',
      price: [Object],
      time: 2025-08-26T13:11:01.991Z,
      instrumentUid: '2f243f46-34ce-4d50-a931-c6f8a67eb758',
      lastPriceType: 1
    }
  ]
}
2025-08-26T13:11:31.768Z bot:provider lastPrice { units: 10, nano: 150000000 }
2025-08-26T13:11:36.770Z bot:balancer newPosition {
  pair: 'TLCB/RUB',
  base: 'TLCB',
  quote: 'RUB',
  figi: 'TCS20A107597',
  price: { units: 10, nano: 150000000 },
  priceNumber: 10.15,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 10, nano: 150000000 }
}
2025-08-26T13:11:36.770Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:11:36.770Z bot:balancer positionIndex -1
2025-08-26T13:11:36.770Z bot:balancer Ticker from DesireWallet not found in portfolio. Creating.
2025-08-26T13:11:36.772Z bot:balancer {
  figi: 'TCS70A106DL2',
  ticker: 'TMON@',
  classCode: 'SPBRU',
  isin: 'RU000A106DL2',
  lot: 1,
  currency: 'rub',
  klong: { units: 2, nano: 0 },
  kshort: { units: 2, nano: 0 },
  dlong: { units: 0, nano: 66600000 },
  dshort: undefined,
  dlongMin: { units: 0, nano: 50000000 },
  dshortMin: undefined,
  shortEnabledFlag: false,
  name: 'Денежный рынок',
  exchange: 'spb_etf_ru',
  fixedCommission: { units: 1, nano: 0 },
  focusType: 'equity',
  releasedDate: 2022-12-22T00:00:00.000Z,
  numShares: undefined,
  countryOfRisk: 'RU',
  countryOfRiskName: 'Российская Федерация',
  sector: 'other',
  rebalancingFreq: '',
  tradingStatus: 5,
  otcFlag: false,
  buyAvailableFlag: true,
  sellAvailableFlag: true,
  minPriceIncrement: { units: 0, nano: 10000000 },
  apiTradeAvailableFlag: true,
  uid: '498ec3ff-ef27-4729-9703-a5aac48d5789',
  realExchange: 2,
  positionUid: '96a4604c-79d9-4a30-8a46-95a80dfd9f02',
  assetUid: 'e16bc6c8-63c2-4023-8c95-e0d212aac4b8',
  instrumentExchange: 0,
  forIisFlag: true,
  forQualInvestorFlag: false,
  weekendFlag: false,
  blockedTcaFlag: false,
  liquidityFlag: true,
  first1minCandleDate: 2025-02-25T07:00:00.000Z,
  first1dayCandleDate: 2025-02-25T00:00:00.000Z,
  brand: {
    logoName: 'TMON2.png',
    logoBaseColor: '#000000',
    textColor: '#ffffff'
  },
  dlongClient: { units: 0, nano: 50000000 },
  dshortClient: { units: 0, nano: 0 }
}
2025-08-26T13:11:36.772Z bot:balancer TCS70A106DL2
2025-08-26T13:11:36.772Z bot:balancer 1
2025-08-26T13:11:36.772Z bot:provider Getting last price
2025-08-26T13:11:37.025Z bot:provider lastPriceResult {
  lastPrices: [
    {
      figi: 'TCS70A106DL2',
      price: [Object],
      time: 2025-08-26T13:11:35.825Z,
      instrumentUid: '498ec3ff-ef27-4729-9703-a5aac48d5789',
      lastPriceType: 1
    }
  ]
}
2025-08-26T13:11:37.025Z bot:provider lastPrice { units: 141, nano: ********* }
2025-08-26T13:11:42.053Z bot:balancer newPosition {
  pair: 'TMON/RUB',
  base: 'TMON',
  quote: 'RUB',
  figi: 'TCS70A106DL2',
  price: { units: 141, nano: ********* },
  priceNumber: 141.7,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 141, nano: ********* }
}
2025-08-26T13:11:42.070Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:11:42.071Z bot:balancer positionIndex 4
2025-08-26T13:11:42.071Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:11:42.071Z bot:balancer positionIndex -1
2025-08-26T13:11:42.071Z bot:balancer Ticker from DesireWallet not found in portfolio. Creating.
2025-08-26T13:11:42.073Z bot:balancer {
  figi: 'TCS70A10A1L8',
  ticker: 'TOFZ@',
  classCode: 'SPBRU',
  isin: 'RU000A10A1L8',
  lot: 1,
  currency: 'rub',
  klong: { units: 2, nano: 0 },
  kshort: { units: 2, nano: 0 },
  dlong: { units: 0, nano: 250000000 },
  dshort: { units: 0, nano: 300000000 },
  dlongMin: { units: 0, nano: ********* },
  dshortMin: { units: 0, nano: 250000000 },
  shortEnabledFlag: true,
  name: 'Государственные облигации',
  exchange: 'spb_etf_ru_noweekend',
  fixedCommission: { units: 1, nano: 500000000 },
  focusType: 'fixed_income',
  releasedDate: 2024-11-07T00:00:00.000Z,
  numShares: undefined,
  countryOfRisk: 'RU',
  countryOfRiskName: 'Российская Федерация',
  sector: 'other',
  rebalancingFreq: 'semi_annual',
  tradingStatus: 5,
  otcFlag: false,
  buyAvailableFlag: true,
  sellAvailableFlag: true,
  minPriceIncrement: { units: 0, nano: 10000000 },
  apiTradeAvailableFlag: true,
  uid: 'c5049184-ded4-49d0-8e14-bffefc40a223',
  realExchange: 2,
  positionUid: '83fd264c-28fc-4b6a-b8f2-ae090148050d',
  assetUid: 'a20a8e51-dd4e-4ba3-beb0-a9bf4dd0a983',
  instrumentExchange: 0,
  forIisFlag: true,
  forQualInvestorFlag: false,
  weekendFlag: false,
  blockedTcaFlag: false,
  liquidityFlag: true,
  first1minCandleDate: 2024-12-09T07:02:00.000Z,
  first1dayCandleDate: 2024-12-09T00:00:00.000Z,
  brand: {
    logoName: 'TOFZ.png',
    logoBaseColor: '#000000',
    textColor: '#ffffff'
  },
  dlongClient: { units: 0, nano: ********* },
  dshortClient: { units: 0, nano: 250000000 }
}
2025-08-26T13:11:42.074Z bot:balancer TCS70A10A1L8
2025-08-26T13:11:42.074Z bot:balancer 1
2025-08-26T13:11:42.074Z bot:provider Getting last price
2025-08-26T13:11:42.337Z bot:provider lastPriceResult {
  lastPrices: [
    {
      figi: 'TCS70A10A1L8',
      price: [Object],
      time: 2025-08-26T13:11:27.261Z,
      instrumentUid: 'c5049184-ded4-49d0-8e14-bffefc40a223',
      lastPriceType: 1
    }
  ]
}
2025-08-26T13:11:42.337Z bot:provider lastPrice { units: 13, nano: 290000000 }
2025-08-26T13:11:47.339Z bot:balancer newPosition {
  pair: 'TOFZ/RUB',
  base: 'TOFZ',
  quote: 'RUB',
  figi: 'TCS70A10A1L8',
  price: { units: 13, nano: 290000000 },
  priceNumber: 13.29,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 13, nano: 290000000 }
}
2025-08-26T13:11:47.340Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:11:47.340Z bot:balancer positionIndex 0
2025-08-26T13:11:47.340Z bot:balancer Calculating totalPrice
2025-08-26T13:11:47.341Z bot:balancer walletWithtotalPrice: map start: position {
  pair: 'RUB/RUB',
  base: 'RUB',
  quote: 'RUB',
  figi: undefined,
  amount: -360,
  lotSize: 1,
  price: { units: 1, nano: 0 },
  priceNumber: 1,
  lotPrice: { units: 1, nano: 0 }
}
2025-08-26T13:11:47.341Z bot:balancer lotPriceNumber 1
2025-08-26T13:11:47.341Z bot:balancer position.amount, position.priceNumber
2025-08-26T13:11:47.341Z bot:balancer -360 1
2025-08-26T13:11:47.341Z bot:balancer totalPriceNumber -360
2025-08-26T13:11:47.341Z bot:balancer totalPrice { units: -360, nano: 0 }
2025-08-26T13:11:47.342Z bot:balancer walletWithtotalPrice: map end: position {
  pair: 'RUB/RUB',
  base: 'RUB',
  quote: 'RUB',
  figi: undefined,
  amount: -360,
  lotSize: 1,
  price: { units: 1, nano: 0 },
  priceNumber: 1,
  lotPrice: { units: 1, nano: 0 },
  totalPrice: { units: -360, nano: 0 }
}
2025-08-26T13:11:47.342Z bot:balancer walletWithtotalPrice: map start: position {
  pair: 'TDIV@/RUB',
  base: 'TDIV@',
  quote: 'RUB',
  figi: 'TCS10A107563',
  amount: 12,
  lotSize: 1,
  price: { units: 10, nano: 550000000 },
  priceNumber: 10.55,
  lotPrice: { units: 10, nano: 550000000 },
  totalPrice: { units: 126, nano: ********* },
  totalPriceNumber: 126.*********00001
}
2025-08-26T13:11:47.342Z bot:balancer lotPriceNumber 10.55
2025-08-26T13:11:47.342Z bot:balancer position.amount, position.priceNumber
2025-08-26T13:11:47.342Z bot:balancer 12 10.55
2025-08-26T13:11:47.342Z bot:balancer totalPriceNumber 126.*********00001
2025-08-26T13:11:47.342Z bot:balancer totalPrice { units: 126, nano: ********* }
2025-08-26T13:11:47.342Z bot:balancer walletWithtotalPrice: map end: position {
  pair: 'TDIV@/RUB',
  base: 'TDIV@',
  quote: 'RUB',
  figi: 'TCS10A107563',
  amount: 12,
  lotSize: 1,
  price: { units: 10, nano: 550000000 },
  priceNumber: 10.55,
  lotPrice: { units: 10, nano: 550000000 },
  totalPrice: { units: 126, nano: ********* },
  totalPriceNumber: 126.*********00001
}
2025-08-26T13:11:47.342Z bot:balancer walletWithtotalPrice: map start: position {
  pair: 'TGLD@/RUB',
  base: 'TGLD@',
  quote: 'RUB',
  figi: 'TCS80A101X50',
  amount: 12,
  lotSize: 1,
  price: { units: 10, nano: 790000000 },
  priceNumber: 10.79,
  lotPrice: { units: 10, nano: 790000000 },
  totalPrice: { units: 129, nano: 4******** },
  totalPriceNumber: 129.48
}
2025-08-26T13:11:47.342Z bot:balancer lotPriceNumber 10.79
2025-08-26T13:11:47.342Z bot:balancer position.amount, position.priceNumber
2025-08-26T13:11:47.342Z bot:balancer 12 10.79
2025-08-26T13:11:47.342Z bot:balancer totalPriceNumber 129.48
2025-08-26T13:11:47.342Z bot:balancer totalPrice { units: 129, nano: 4******** }
2025-08-26T13:11:47.342Z bot:balancer walletWithtotalPrice: map end: position {
  pair: 'TGLD@/RUB',
  base: 'TGLD@',
  quote: 'RUB',
  figi: 'TCS80A101X50',
  amount: 12,
  lotSize: 1,
  price: { units: 10, nano: 790000000 },
  priceNumber: 10.79,
  lotPrice: { units: 10, nano: 790000000 },
  totalPrice: { units: 129, nano: 4******** },
  totalPriceNumber: 129.48
}
2025-08-26T13:11:47.342Z bot:balancer walletWithtotalPrice: map start: position {
  pair: 'TRUR/RUB',
  base: 'TRUR',
  quote: 'RUB',
  figi: 'BBG000000001',
  amount: 14,
  lotSize: 1,
  price: { units: 9, nano: 300000000 },
  priceNumber: 9.3,
  lotPrice: { units: 9, nano: 300000000 },
  totalPrice: { units: 130, nano: ********* },
  totalPriceNumber: 130.*********00002
}
2025-08-26T13:11:47.342Z bot:balancer lotPriceNumber 9.3
2025-08-26T13:11:47.342Z bot:balancer position.amount, position.priceNumber
2025-08-26T13:11:47.342Z bot:balancer 14 9.3
2025-08-26T13:11:47.343Z bot:balancer totalPriceNumber 130.*********00002
2025-08-26T13:11:47.343Z bot:balancer totalPrice { units: 130, nano: ********* }
2025-08-26T13:11:47.343Z bot:balancer walletWithtotalPrice: map end: position {
  pair: 'TRUR/RUB',
  base: 'TRUR',
  quote: 'RUB',
  figi: 'BBG000000001',
  amount: 14,
  lotSize: 1,
  price: { units: 9, nano: 300000000 },
  priceNumber: 9.3,
  lotPrice: { units: 9, nano: 300000000 },
  totalPrice: { units: 130, nano: ********* },
  totalPriceNumber: 130.*********00002
}
2025-08-26T13:11:47.343Z bot:balancer walletWithtotalPrice: map start: position {
  pair: 'TMOS@/RUB',
  base: 'TMOS@',
  quote: 'RUB',
  figi: 'TCS60A101X76',
  amount: 18,
  lotSize: 1,
  price: { units: 6, nano: ********* },
  priceNumber: 6.7,
  lotPrice: { units: 6, nano: ********* },
  totalPrice: { units: 120, nano: ********* },
  totalPriceNumber: 120.*********00001
}
2025-08-26T13:11:47.343Z bot:balancer lotPriceNumber 6.7
2025-08-26T13:11:47.343Z bot:balancer position.amount, position.priceNumber
2025-08-26T13:11:47.343Z bot:balancer 18 6.7
2025-08-26T13:11:47.343Z bot:balancer totalPriceNumber 120.*********00001
2025-08-26T13:11:47.343Z bot:balancer totalPrice { units: 120, nano: ********* }
2025-08-26T13:11:47.343Z bot:balancer walletWithtotalPrice: map end: position {
  pair: 'TMOS@/RUB',
  base: 'TMOS@',
  quote: 'RUB',
  figi: 'TCS60A101X76',
  amount: 18,
  lotSize: 1,
  price: { units: 6, nano: ********* },
  priceNumber: 6.7,
  lotPrice: { units: 6, nano: ********* },
  totalPrice: { units: 120, nano: ********* },
  totalPriceNumber: 120.*********00001
}
2025-08-26T13:11:47.343Z bot:balancer walletWithtotalPrice: map start: position {
  pair: 'TRND/RUB',
  base: 'TRND',
  quote: 'RUB',
  figi: 'TCS00A10B0G9',
  amount: 24,
  lotSize: 1,
  price: { units: 9, nano: 890000000 },
  priceNumber: 9.88,
  lotPrice: { units: 9, nano: 890000000 },
  totalPrice: { units: 237, nano: 120000000 },
  totalPriceNumber: 237.12
}
2025-08-26T13:11:47.343Z bot:balancer lotPriceNumber 9.89
2025-08-26T13:11:47.343Z bot:balancer position.amount, position.priceNumber
2025-08-26T13:11:47.343Z bot:balancer 24 9.88
2025-08-26T13:11:47.343Z bot:balancer totalPriceNumber 237.36
2025-08-26T13:11:47.343Z bot:balancer totalPrice { units: 237, nano: 360000000 }
2025-08-26T13:11:47.343Z bot:balancer walletWithtotalPrice: map end: position {
  pair: 'TRND/RUB',
  base: 'TRND',
  quote: 'RUB',
  figi: 'TCS00A10B0G9',
  amount: 24,
  lotSize: 1,
  price: { units: 9, nano: 890000000 },
  priceNumber: 9.88,
  lotPrice: { units: 9, nano: 890000000 },
  totalPrice: { units: 237, nano: 360000000 },
  totalPriceNumber: 237.12
}
2025-08-26T13:11:47.343Z bot:balancer walletWithtotalPrice: map start: position {
  pair: 'TPAY/RUB',
  base: 'TPAY',
  quote: 'RUB',
  figi: 'TCS00A108WX3',
  price: { units: 101, nano: 690000000 },
  priceNumber: 101.69,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 101, nano: 690000000 }
}
2025-08-26T13:11:47.343Z bot:balancer lotPriceNumber 101.69
2025-08-26T13:11:47.343Z bot:balancer position.amount, position.priceNumber
2025-08-26T13:11:47.343Z bot:balancer 0 101.69
2025-08-26T13:11:47.344Z bot:balancer walletWithtotalPrice: map end: position {
  pair: 'TPAY/RUB',
  base: 'TPAY',
  quote: 'RUB',
  figi: 'TCS00A108WX3',
  price: { units: 101, nano: 690000000 },
  priceNumber: 101.69,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 101, nano: 690000000 }
}
2025-08-26T13:11:47.344Z bot:balancer walletWithtotalPrice: map start: position {
  pair: 'TBRU/RUB',
  base: 'TBRU',
  quote: 'RUB',
  figi: 'TCS60A1039N1',
  price: { units: 7, nano: 400000000 },
  priceNumber: 7.4,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 7, nano: 400000000 }
}
2025-08-26T13:11:47.344Z bot:balancer lotPriceNumber 7.4
2025-08-26T13:11:47.344Z bot:balancer position.amount, position.priceNumber
2025-08-26T13:11:47.344Z bot:balancer 0 7.4
2025-08-26T13:11:47.344Z bot:balancer walletWithtotalPrice: map end: position {
  pair: 'TBRU/RUB',
  base: 'TBRU',
  quote: 'RUB',
  figi: 'TCS60A1039N1',
  price: { units: 7, nano: 400000000 },
  priceNumber: 7.4,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 7, nano: 400000000 }
}
2025-08-26T13:11:47.344Z bot:balancer walletWithtotalPrice: map start: position {
  pair: 'TITR/RUB',
  base: 'TITR',
  quote: 'RUB',
  figi: 'TCS30A108BL2',
  price: { units: 6, nano: 660000000 },
  priceNumber: 6.66,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 6, nano: 660000000 }
}
2025-08-26T13:11:47.344Z bot:balancer lotPriceNumber 6.66
2025-08-26T13:11:47.344Z bot:balancer position.amount, position.priceNumber
2025-08-26T13:11:47.344Z bot:balancer 0 6.66
2025-08-26T13:11:47.344Z bot:balancer walletWithtotalPrice: map end: position {
  pair: 'TITR/RUB',
  base: 'TITR',
  quote: 'RUB',
  figi: 'TCS30A108BL2',
  price: { units: 6, nano: 660000000 },
  priceNumber: 6.66,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 6, nano: 660000000 }
}
2025-08-26T13:11:47.344Z bot:balancer walletWithtotalPrice: map start: position {
  pair: 'TLCB/RUB',
  base: 'TLCB',
  quote: 'RUB',
  figi: 'TCS20A107597',
  price: { units: 10, nano: 150000000 },
  priceNumber: 10.15,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 10, nano: 150000000 }
}
2025-08-26T13:11:47.344Z bot:balancer lotPriceNumber 10.15
2025-08-26T13:11:47.344Z bot:balancer position.amount, position.priceNumber
2025-08-26T13:11:47.344Z bot:balancer 0 10.15
2025-08-26T13:11:47.344Z bot:balancer walletWithtotalPrice: map end: position {
  pair: 'TLCB/RUB',
  base: 'TLCB',
  quote: 'RUB',
  figi: 'TCS20A107597',
  price: { units: 10, nano: 150000000 },
  priceNumber: 10.15,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 10, nano: 150000000 }
}
2025-08-26T13:11:47.344Z bot:balancer walletWithtotalPrice: map start: position {
  pair: 'TMON/RUB',
  base: 'TMON',
  quote: 'RUB',
  figi: 'TCS70A106DL2',
  price: { units: 141, nano: ********* },
  priceNumber: 141.7,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 141, nano: ********* }
}
2025-08-26T13:11:47.344Z bot:balancer lotPriceNumber 141.7
2025-08-26T13:11:47.344Z bot:balancer position.amount, position.priceNumber
2025-08-26T13:11:47.344Z bot:balancer 0 141.7
2025-08-26T13:11:47.344Z bot:balancer walletWithtotalPrice: map end: position {
  pair: 'TMON/RUB',
  base: 'TMON',
  quote: 'RUB',
  figi: 'TCS70A106DL2',
  price: { units: 141, nano: ********* },
  priceNumber: 141.7,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 141, nano: ********* }
}
2025-08-26T13:11:47.344Z bot:balancer walletWithtotalPrice: map start: position {
  pair: 'TOFZ/RUB',
  base: 'TOFZ',
  quote: 'RUB',
  figi: 'TCS70A10A1L8',
  price: { units: 13, nano: 290000000 },
  priceNumber: 13.29,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 13, nano: 290000000 }
}
2025-08-26T13:11:47.345Z bot:balancer lotPriceNumber 13.29
2025-08-26T13:11:47.345Z bot:balancer position.amount, position.priceNumber
2025-08-26T13:11:47.345Z bot:balancer 0 13.29
2025-08-26T13:11:47.345Z bot:balancer walletWithtotalPrice: map end: position {
  pair: 'TOFZ/RUB',
  base: 'TOFZ',
  quote: 'RUB',
  figi: 'TCS70A10A1L8',
  price: { units: 13, nano: 290000000 },
  priceNumber: 13.29,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 13, nano: 290000000 }
}
2025-08-26T13:11:47.345Z bot:balancer addNumbersToPosition start
2025-08-26T13:11:47.345Z bot:balancer position.price { units: 1, nano: 0 }
2025-08-26T13:11:47.345Z bot:balancer position.priceNumber 1
2025-08-26T13:11:47.345Z bot:balancer position.lotPrice { units: 1, nano: 0 }
2025-08-26T13:11:47.345Z bot:balancer position.lotPriceNumber 1
2025-08-26T13:11:47.345Z bot:balancer position.totalPrice { units: -360, nano: 0 }
2025-08-26T13:11:47.345Z bot:balancer position.totalPriceNumber -360
2025-08-26T13:11:47.345Z bot:balancer addNumbersToPosition end {
  pair: 'RUB/RUB',
  base: 'RUB',
  quote: 'RUB',
  figi: undefined,
  amount: -360,
  lotSize: 1,
  price: { units: 1, nano: 0 },
  priceNumber: 1,
  lotPrice: { units: 1, nano: 0 },
  totalPrice: { units: -360, nano: 0 },
  lotPriceNumber: 1,
  totalPriceNumber: -360
}
2025-08-26T13:11:47.345Z bot:balancer addNumbersToPosition start
2025-08-26T13:11:47.345Z bot:balancer position.price { units: 10, nano: 550000000 }
2025-08-26T13:11:47.345Z bot:balancer position.priceNumber 10.55
2025-08-26T13:11:47.345Z bot:balancer position.lotPrice { units: 10, nano: 550000000 }
2025-08-26T13:11:47.345Z bot:balancer position.lotPriceNumber 10.55
2025-08-26T13:11:47.345Z bot:balancer position.totalPrice { units: 126, nano: ********* }
2025-08-26T13:11:47.345Z bot:balancer position.totalPriceNumber 126.6
2025-08-26T13:11:47.345Z bot:balancer addNumbersToPosition end {
  pair: 'TDIV@/RUB',
  base: 'TDIV@',
  quote: 'RUB',
  figi: 'TCS10A107563',
  amount: 12,
  lotSize: 1,
  price: { units: 10, nano: 550000000 },
  priceNumber: 10.55,
  lotPrice: { units: 10, nano: 550000000 },
  totalPrice: { units: 126, nano: ********* },
  totalPriceNumber: 126.6,
  lotPriceNumber: 10.55
}
2025-08-26T13:11:47.346Z bot:balancer addNumbersToPosition start
2025-08-26T13:11:47.346Z bot:balancer position.price { units: 10, nano: 790000000 }
2025-08-26T13:11:47.346Z bot:balancer position.priceNumber 10.79
2025-08-26T13:11:47.346Z bot:balancer position.lotPrice { units: 10, nano: 790000000 }
2025-08-26T13:11:47.346Z bot:balancer position.lotPriceNumber 10.79
2025-08-26T13:11:47.346Z bot:balancer position.totalPrice { units: 129, nano: 4******** }
2025-08-26T13:11:47.346Z bot:balancer position.totalPriceNumber 129.48
2025-08-26T13:11:47.346Z bot:balancer addNumbersToPosition end {
  pair: 'TGLD@/RUB',
  base: 'TGLD@',
  quote: 'RUB',
  figi: 'TCS80A101X50',
  amount: 12,
  lotSize: 1,
  price: { units: 10, nano: 790000000 },
  priceNumber: 10.79,
  lotPrice: { units: 10, nano: 790000000 },
  totalPrice: { units: 129, nano: 4******** },
  totalPriceNumber: 129.48,
  lotPriceNumber: 10.79
}
2025-08-26T13:11:47.346Z bot:balancer addNumbersToPosition start
2025-08-26T13:11:47.346Z bot:balancer position.price { units: 9, nano: 300000000 }
2025-08-26T13:11:47.346Z bot:balancer position.priceNumber 9.3
2025-08-26T13:11:47.346Z bot:balancer position.lotPrice { units: 9, nano: 300000000 }
2025-08-26T13:11:47.346Z bot:balancer position.lotPriceNumber 9.3
2025-08-26T13:11:47.346Z bot:balancer position.totalPrice { units: 130, nano: ********* }
2025-08-26T13:11:47.346Z bot:balancer position.totalPriceNumber 130.2
2025-08-26T13:11:47.346Z bot:balancer addNumbersToPosition end {
  pair: 'TRUR/RUB',
  base: 'TRUR',
  quote: 'RUB',
  figi: 'BBG000000001',
  amount: 14,
  lotSize: 1,
  price: { units: 9, nano: 300000000 },
  priceNumber: 9.3,
  lotPrice: { units: 9, nano: 300000000 },
  totalPrice: { units: 130, nano: ********* },
  totalPriceNumber: 130.2,
  lotPriceNumber: 9.3
}
2025-08-26T13:11:47.346Z bot:balancer addNumbersToPosition start
2025-08-26T13:11:47.346Z bot:balancer position.price { units: 6, nano: ********* }
2025-08-26T13:11:47.346Z bot:balancer position.priceNumber 6.7
2025-08-26T13:11:47.346Z bot:balancer position.lotPrice { units: 6, nano: ********* }
2025-08-26T13:11:47.346Z bot:balancer position.lotPriceNumber 6.7
2025-08-26T13:11:47.346Z bot:balancer position.totalPrice { units: 120, nano: ********* }
2025-08-26T13:11:47.346Z bot:balancer position.totalPriceNumber 120.6
2025-08-26T13:11:47.346Z bot:balancer addNumbersToPosition end {
  pair: 'TMOS@/RUB',
  base: 'TMOS@',
  quote: 'RUB',
  figi: 'TCS60A101X76',
  amount: 18,
  lotSize: 1,
  price: { units: 6, nano: ********* },
  priceNumber: 6.7,
  lotPrice: { units: 6, nano: ********* },
  totalPrice: { units: 120, nano: ********* },
  totalPriceNumber: 120.6,
  lotPriceNumber: 6.7
}
2025-08-26T13:11:47.346Z bot:balancer addNumbersToPosition start
2025-08-26T13:11:47.346Z bot:balancer position.price { units: 9, nano: 890000000 }
2025-08-26T13:11:47.346Z bot:balancer position.priceNumber 9.89
2025-08-26T13:11:47.346Z bot:balancer position.lotPrice { units: 9, nano: 890000000 }
2025-08-26T13:11:47.346Z bot:balancer position.lotPriceNumber 9.89
2025-08-26T13:11:47.346Z bot:balancer position.totalPrice { units: 237, nano: 360000000 }
2025-08-26T13:11:47.346Z bot:balancer position.totalPriceNumber 237.36
2025-08-26T13:11:47.346Z bot:balancer addNumbersToPosition end {
  pair: 'TRND/RUB',
  base: 'TRND',
  quote: 'RUB',
  figi: 'TCS00A10B0G9',
  amount: 24,
  lotSize: 1,
  price: { units: 9, nano: 890000000 },
  priceNumber: 9.89,
  lotPrice: { units: 9, nano: 890000000 },
  totalPrice: { units: 237, nano: 360000000 },
  totalPriceNumber: 237.36,
  lotPriceNumber: 9.89
}
2025-08-26T13:11:47.346Z bot:balancer addNumbersToPosition start
2025-08-26T13:11:47.346Z bot:balancer position.price { units: 101, nano: 690000000 }
2025-08-26T13:11:47.346Z bot:balancer position.priceNumber 101.69
2025-08-26T13:11:47.346Z bot:balancer position.lotPrice { units: 101, nano: 690000000 }
2025-08-26T13:11:47.346Z bot:balancer position.lotPriceNumber 101.69
2025-08-26T13:11:47.346Z bot:balancer position.totalPrice undefined
2025-08-26T13:11:47.346Z bot:balancer addNumbersToPosition end {
  pair: 'TPAY/RUB',
  base: 'TPAY',
  quote: 'RUB',
  figi: 'TCS00A108WX3',
  price: { units: 101, nano: 690000000 },
  priceNumber: 101.69,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 101, nano: 690000000 },
  lotPriceNumber: 101.69
}
2025-08-26T13:11:47.347Z bot:balancer addNumbersToPosition start
2025-08-26T13:11:47.347Z bot:balancer position.price { units: 7, nano: 400000000 }
2025-08-26T13:11:47.347Z bot:balancer position.priceNumber 7.4
2025-08-26T13:11:47.347Z bot:balancer position.lotPrice { units: 7, nano: 400000000 }
2025-08-26T13:11:47.347Z bot:balancer position.lotPriceNumber 7.4
2025-08-26T13:11:47.347Z bot:balancer position.totalPrice undefined
2025-08-26T13:11:47.347Z bot:balancer addNumbersToPosition end {
  pair: 'TBRU/RUB',
  base: 'TBRU',
  quote: 'RUB',
  figi: 'TCS60A1039N1',
  price: { units: 7, nano: 400000000 },
  priceNumber: 7.4,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 7, nano: 400000000 },
  lotPriceNumber: 7.4
}
2025-08-26T13:11:47.347Z bot:balancer addNumbersToPosition start
2025-08-26T13:11:47.347Z bot:balancer position.price { units: 6, nano: 660000000 }
2025-08-26T13:11:47.347Z bot:balancer position.priceNumber 6.66
2025-08-26T13:11:47.347Z bot:balancer position.lotPrice { units: 6, nano: 660000000 }
2025-08-26T13:11:47.347Z bot:balancer position.lotPriceNumber 6.66
2025-08-26T13:11:47.347Z bot:balancer position.totalPrice undefined
2025-08-26T13:11:47.347Z bot:balancer addNumbersToPosition end {
  pair: 'TITR/RUB',
  base: 'TITR',
  quote: 'RUB',
  figi: 'TCS30A108BL2',
  price: { units: 6, nano: 660000000 },
  priceNumber: 6.66,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 6, nano: 660000000 },
  lotPriceNumber: 6.66
}
2025-08-26T13:11:47.347Z bot:balancer addNumbersToPosition start
2025-08-26T13:11:47.347Z bot:balancer position.price { units: 10, nano: 150000000 }
2025-08-26T13:11:47.347Z bot:balancer position.priceNumber 10.15
2025-08-26T13:11:47.347Z bot:balancer position.lotPrice { units: 10, nano: 150000000 }
2025-08-26T13:11:47.347Z bot:balancer position.lotPriceNumber 10.15
2025-08-26T13:11:47.347Z bot:balancer position.totalPrice undefined
2025-08-26T13:11:47.347Z bot:balancer addNumbersToPosition end {
  pair: 'TLCB/RUB',
  base: 'TLCB',
  quote: 'RUB',
  figi: 'TCS20A107597',
  price: { units: 10, nano: 150000000 },
  priceNumber: 10.15,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 10, nano: 150000000 },
  lotPriceNumber: 10.15
}
2025-08-26T13:11:47.347Z bot:balancer addNumbersToPosition start
2025-08-26T13:11:47.347Z bot:balancer position.price { units: 141, nano: ********* }
2025-08-26T13:11:47.347Z bot:balancer position.priceNumber 141.7
2025-08-26T13:11:47.347Z bot:balancer position.lotPrice { units: 141, nano: ********* }
2025-08-26T13:11:47.347Z bot:balancer position.lotPriceNumber 141.7
2025-08-26T13:11:47.347Z bot:balancer position.totalPrice undefined
2025-08-26T13:11:47.347Z bot:balancer addNumbersToPosition end {
  pair: 'TMON/RUB',
  base: 'TMON',
  quote: 'RUB',
  figi: 'TCS70A106DL2',
  price: { units: 141, nano: ********* },
  priceNumber: 141.7,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 141, nano: ********* },
  lotPriceNumber: 141.7
}
2025-08-26T13:11:47.348Z bot:balancer addNumbersToPosition start
2025-08-26T13:11:47.348Z bot:balancer position.price { units: 13, nano: 290000000 }
2025-08-26T13:11:47.348Z bot:balancer position.priceNumber 13.29
2025-08-26T13:11:47.348Z bot:balancer position.lotPrice { units: 13, nano: 290000000 }
2025-08-26T13:11:47.348Z bot:balancer position.lotPriceNumber 13.29
2025-08-26T13:11:47.348Z bot:balancer position.totalPrice undefined
2025-08-26T13:11:47.348Z bot:balancer addNumbersToPosition end {
  pair: 'TOFZ/RUB',
  base: 'TOFZ',
  quote: 'RUB',
  figi: 'TCS70A10A1L8',
  price: { units: 13, nano: 290000000 },
  priceNumber: 13.29,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 13, nano: 290000000 },
  lotPriceNumber: 13.29
}
2025-08-26T13:11:47.348Z bot:balancer addNumbersToWallet [
  {
    pair: 'RUB/RUB',
    base: 'RUB',
    quote: 'RUB',
    figi: undefined,
    amount: -360,
    lotSize: 1,
    price: { units: 1, nano: 0 },
    priceNumber: 1,
    lotPrice: { units: 1, nano: 0 },
    totalPrice: { units: -360, nano: 0 },
    lotPriceNumber: 1,
    totalPriceNumber: -360
  },
  {
    pair: 'TDIV@/RUB',
    base: 'TDIV@',
    quote: 'RUB',
    figi: 'TCS10A107563',
    amount: 12,
    lotSize: 1,
    price: { units: 10, nano: 550000000 },
    priceNumber: 10.55,
    lotPrice: { units: 10, nano: 550000000 },
    totalPrice: { units: 126, nano: ********* },
    totalPriceNumber: 126.6,
    lotPriceNumber: 10.55
  },
  {
    pair: 'TGLD@/RUB',
    base: 'TGLD@',
    quote: 'RUB',
    figi: 'TCS80A101X50',
    amount: 12,
    lotSize: 1,
    price: { units: 10, nano: 790000000 },
    priceNumber: 10.79,
    lotPrice: { units: 10, nano: 790000000 },
    totalPrice: { units: 129, nano: 4******** },
    totalPriceNumber: 129.48,
    lotPriceNumber: 10.79
  },
  {
    pair: 'TRUR/RUB',
    base: 'TRUR',
    quote: 'RUB',
    figi: 'BBG000000001',
    amount: 14,
    lotSize: 1,
    price: { units: 9, nano: 300000000 },
    priceNumber: 9.3,
    lotPrice: { units: 9, nano: 300000000 },
    totalPrice: { units: 130, nano: ********* },
    totalPriceNumber: 130.2,
    lotPriceNumber: 9.3
  },
  {
    pair: 'TMOS@/RUB',
    base: 'TMOS@',
    quote: 'RUB',
    figi: 'TCS60A101X76',
    amount: 18,
    lotSize: 1,
    price: { units: 6, nano: ********* },
    priceNumber: 6.7,
    lotPrice: { units: 6, nano: ********* },
    totalPrice: { units: 120, nano: ********* },
    totalPriceNumber: 120.6,
    lotPriceNumber: 6.7
  },
  {
    pair: 'TRND/RUB',
    base: 'TRND',
    quote: 'RUB',
    figi: 'TCS00A10B0G9',
    amount: 24,
    lotSize: 1,
    price: { units: 9, nano: 890000000 },
    priceNumber: 9.89,
    lotPrice: { units: 9, nano: 890000000 },
    totalPrice: { units: 237, nano: 360000000 },
    totalPriceNumber: 237.36,
    lotPriceNumber: 9.89
  },
  {
    pair: 'TPAY/RUB',
    base: 'TPAY',
    quote: 'RUB',
    figi: 'TCS00A108WX3',
    price: { units: 101, nano: 690000000 },
    priceNumber: 101.69,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 101, nano: 690000000 },
    lotPriceNumber: 101.69
  },
  {
    pair: 'TBRU/RUB',
    base: 'TBRU',
    quote: 'RUB',
    figi: 'TCS60A1039N1',
    price: { units: 7, nano: 400000000 },
    priceNumber: 7.4,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 7, nano: 400000000 },
    lotPriceNumber: 7.4
  },
  {
    pair: 'TITR/RUB',
    base: 'TITR',
    quote: 'RUB',
    figi: 'TCS30A108BL2',
    price: { units: 6, nano: 660000000 },
    priceNumber: 6.66,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 6, nano: 660000000 },
    lotPriceNumber: 6.66
  },
  {
    pair: 'TLCB/RUB',
    base: 'TLCB',
    quote: 'RUB',
    figi: 'TCS20A107597',
    price: { units: 10, nano: 150000000 },
    priceNumber: 10.15,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 10, nano: 150000000 },
    lotPriceNumber: 10.15
  },
  {
    pair: 'TMON/RUB',
    base: 'TMON',
    quote: 'RUB',
    figi: 'TCS70A106DL2',
    price: { units: 141, nano: ********* },
    priceNumber: 141.7,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 141, nano: ********* },
    lotPriceNumber: 141.7
  },
  {
    pair: 'TOFZ/RUB',
    base: 'TOFZ',
    quote: 'RUB',
    figi: 'TCS70A10A1L8',
    price: { units: 13, nano: 290000000 },
    priceNumber: 13.29,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 13, nano: 290000000 },
    lotPriceNumber: 13.29
  }
]
2025-08-26T13:11:47.351Z bot:balancer addNumbersToWallet [Function: addNumbersToWallet]
2025-08-26T13:11:47.354Z bot:balancer sortedWallet [
  {
    pair: 'TMON/RUB',
    base: 'TMON',
    quote: 'RUB',
    figi: 'TCS70A106DL2',
    price: { units: 141, nano: ********* },
    priceNumber: 141.7,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 141, nano: ********* },
    lotPriceNumber: 141.7
  },
  {
    pair: 'TPAY/RUB',
    base: 'TPAY',
    quote: 'RUB',
    figi: 'TCS00A108WX3',
    price: { units: 101, nano: 690000000 },
    priceNumber: 101.69,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 101, nano: 690000000 },
    lotPriceNumber: 101.69
  },
  {
    pair: 'TOFZ/RUB',
    base: 'TOFZ',
    quote: 'RUB',
    figi: 'TCS70A10A1L8',
    price: { units: 13, nano: 290000000 },
    priceNumber: 13.29,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 13, nano: 290000000 },
    lotPriceNumber: 13.29
  },
  {
    pair: 'TGLD@/RUB',
    base: 'TGLD@',
    quote: 'RUB',
    figi: 'TCS80A101X50',
    amount: 12,
    lotSize: 1,
    price: { units: 10, nano: 790000000 },
    priceNumber: 10.79,
    lotPrice: { units: 10, nano: 790000000 },
    totalPrice: { units: 129, nano: 4******** },
    totalPriceNumber: 129.48,
    lotPriceNumber: 10.79
  },
  {
    pair: 'TDIV@/RUB',
    base: 'TDIV@',
    quote: 'RUB',
    figi: 'TCS10A107563',
    amount: 12,
    lotSize: 1,
    price: { units: 10, nano: 550000000 },
    priceNumber: 10.55,
    lotPrice: { units: 10, nano: 550000000 },
    totalPrice: { units: 126, nano: ********* },
    totalPriceNumber: 126.6,
    lotPriceNumber: 10.55
  },
  {
    pair: 'TLCB/RUB',
    base: 'TLCB',
    quote: 'RUB',
    figi: 'TCS20A107597',
    price: { units: 10, nano: 150000000 },
    priceNumber: 10.15,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 10, nano: 150000000 },
    lotPriceNumber: 10.15
  },
  {
    pair: 'TRND/RUB',
    base: 'TRND',
    quote: 'RUB',
    figi: 'TCS00A10B0G9',
    amount: 24,
    lotSize: 1,
    price: { units: 9, nano: 890000000 },
    priceNumber: 9.89,
    lotPrice: { units: 9, nano: 890000000 },
    totalPrice: { units: 237, nano: 360000000 },
    totalPriceNumber: 237.36,
    lotPriceNumber: 9.89
  },
  {
    pair: 'TRUR/RUB',
    base: 'TRUR',
    quote: 'RUB',
    figi: 'BBG000000001',
    amount: 14,
    lotSize: 1,
    price: { units: 9, nano: 300000000 },
    priceNumber: 9.3,
    lotPrice: { units: 9, nano: 300000000 },
    totalPrice: { units: 130, nano: ********* },
    totalPriceNumber: 130.2,
    lotPriceNumber: 9.3
  },
  {
    pair: 'TBRU/RUB',
    base: 'TBRU',
    quote: 'RUB',
    figi: 'TCS60A1039N1',
    price: { units: 7, nano: 400000000 },
    priceNumber: 7.4,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 7, nano: 400000000 },
    lotPriceNumber: 7.4
  },
  {
    pair: 'TMOS@/RUB',
    base: 'TMOS@',
    quote: 'RUB',
    figi: 'TCS60A101X76',
    amount: 18,
    lotSize: 1,
    price: { units: 6, nano: ********* },
    priceNumber: 6.7,
    lotPrice: { units: 6, nano: ********* },
    totalPrice: { units: 120, nano: ********* },
    totalPriceNumber: 120.6,
    lotPriceNumber: 6.7
  },
  {
    pair: 'TITR/RUB',
    base: 'TITR',
    quote: 'RUB',
    figi: 'TCS30A108BL2',
    price: { units: 6, nano: 660000000 },
    priceNumber: 6.66,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 6, nano: 660000000 },
    lotPriceNumber: 6.66
  },
  {
    pair: 'RUB/RUB',
    base: 'RUB',
    quote: 'RUB',
    figi: undefined,
    amount: -360,
    lotSize: 1,
    price: { units: 1, nano: 0 },
    priceNumber: 1,
    lotPrice: { units: 1, nano: 0 },
    totalPrice: { units: -360, nano: 0 },
    lotPriceNumber: 1,
    totalPriceNumber: -360
  }
]
2025-08-26T13:11:47.354Z bot:balancer Summing up all positions in portfolio
2025-08-26T13:11:47.355Z bot:balancer [
  {
    pair: 'TMON/RUB',
    base: 'TMON',
    quote: 'RUB',
    figi: 'TCS70A106DL2',
    price: { units: 141, nano: ********* },
    priceNumber: 141.7,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 141, nano: ********* },
    lotPriceNumber: 141.7
  },
  {
    pair: 'TPAY/RUB',
    base: 'TPAY',
    quote: 'RUB',
    figi: 'TCS00A108WX3',
    price: { units: 101, nano: 690000000 },
    priceNumber: 101.69,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 101, nano: 690000000 },
    lotPriceNumber: 101.69
  },
  {
    pair: 'TOFZ/RUB',
    base: 'TOFZ',
    quote: 'RUB',
    figi: 'TCS70A10A1L8',
    price: { units: 13, nano: 290000000 },
    priceNumber: 13.29,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 13, nano: 290000000 },
    lotPriceNumber: 13.29
  },
  {
    pair: 'TGLD@/RUB',
    base: 'TGLD@',
    quote: 'RUB',
    figi: 'TCS80A101X50',
    amount: 12,
    lotSize: 1,
    price: { units: 10, nano: 790000000 },
    priceNumber: 10.79,
    lotPrice: { units: 10, nano: 790000000 },
    totalPrice: { units: 129, nano: 4******** },
    totalPriceNumber: 129.48,
    lotPriceNumber: 10.79
  },
  {
    pair: 'TDIV@/RUB',
    base: 'TDIV@',
    quote: 'RUB',
    figi: 'TCS10A107563',
    amount: 12,
    lotSize: 1,
    price: { units: 10, nano: 550000000 },
    priceNumber: 10.55,
    lotPrice: { units: 10, nano: 550000000 },
    totalPrice: { units: 126, nano: ********* },
    totalPriceNumber: 126.6,
    lotPriceNumber: 10.55
  },
  {
    pair: 'TLCB/RUB',
    base: 'TLCB',
    quote: 'RUB',
    figi: 'TCS20A107597',
    price: { units: 10, nano: 150000000 },
    priceNumber: 10.15,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 10, nano: 150000000 },
    lotPriceNumber: 10.15
  },
  {
    pair: 'TRND/RUB',
    base: 'TRND',
    quote: 'RUB',
    figi: 'TCS00A10B0G9',
    amount: 24,
    lotSize: 1,
    price: { units: 9, nano: 890000000 },
    priceNumber: 9.89,
    lotPrice: { units: 9, nano: 890000000 },
    totalPrice: { units: 237, nano: 360000000 },
    totalPriceNumber: 237.36,
    lotPriceNumber: 9.89
  },
  {
    pair: 'TRUR/RUB',
    base: 'TRUR',
    quote: 'RUB',
    figi: 'BBG000000001',
    amount: 14,
    lotSize: 1,
    price: { units: 9, nano: 300000000 },
    priceNumber: 9.3,
    lotPrice: { units: 9, nano: 300000000 },
    totalPrice: { units: 130, nano: ********* },
    totalPriceNumber: 130.2,
    lotPriceNumber: 9.3
  },
  {
    pair: 'TBRU/RUB',
    base: 'TBRU',
    quote: 'RUB',
    figi: 'TCS60A1039N1',
    price: { units: 7, nano: 400000000 },
    priceNumber: 7.4,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 7, nano: 400000000 },
    lotPriceNumber: 7.4
  },
  {
    pair: 'TMOS@/RUB',
    base: 'TMOS@',
    quote: 'RUB',
    figi: 'TCS60A101X76',
    amount: 18,
    lotSize: 1,
    price: { units: 6, nano: ********* },
    priceNumber: 6.7,
    lotPrice: { units: 6, nano: ********* },
    totalPrice: { units: 120, nano: ********* },
    totalPriceNumber: 120.6,
    lotPriceNumber: 6.7
  },
  {
    pair: 'TITR/RUB',
    base: 'TITR',
    quote: 'RUB',
    figi: 'TCS30A108BL2',
    price: { units: 6, nano: 660000000 },
    priceNumber: 6.66,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 6, nano: 660000000 },
    lotPriceNumber: 6.66
  },
  {
    pair: 'RUB/RUB',
    base: 'RUB',
    quote: 'RUB',
    figi: undefined,
    amount: -360,
    lotSize: 1,
    price: { units: 1, nano: 0 },
    priceNumber: 1,
    lotPrice: { units: 1, nano: 0 },
    totalPrice: { units: -360, nano: 0 },
    lotPriceNumber: 1,
    totalPriceNumber: -360
  }
]
2025-08-26T13:11:47.355Z bot:balancer walletSumNumber 384.24
2025-08-26T13:11:47.355Z bot:balancer Optimal position sizes: {
  TPAY: {
    baseSize: 64.39722623590109,
    marginSize: 193.19167870770326,
    totalSize: 257.58890494360435
  },
  TGLD: {
    baseSize: 33.81210424311211,
    marginSize: 101.43631272933632,
    totalSize: 135.24841697244844
  },
  TRUR: {
    baseSize: 34.09775152262782,
    marginSize: 102.29325456788345,
    totalSize: 136.39100609051127
  },
  TRND: {
    baseSize: 61.439212857491846,
    marginSize: 184.31763857247554,
    totalSize: 245.75685142996738
  },
  TBRU: {
    baseSize: 25.206821996708513,
    marginSize: 75.62046599012554,
    totalSize: 100.82728798683405
  },
  TDIV: {
    baseSize: 34.17820937698076,
    marginSize: 102.53462813094228,
    totalSize: 136.71283750792304
  },
  TITR: {
    baseSize: 37.353280558724165,
    marginSize: 112.0598416761725,
    totalSize: 149.41312223489666
  },
  TLCB: {
    baseSize: 32.42157199129235,
    marginSize: 97.26471597387706,
    totalSize: 129.6862879651694
  },
  TMON: {
    baseSize: 29.471228125651255,
    marginSize: 88.41368437695377,
    totalSize: 117.88491250260502
  },
  TMOS: {
    baseSize: 31.862593091510057,
    marginSize: 95.58777927453016,
    totalSize: 127.**************
  },
  TOFZ: { baseSize: 0, marginSize: 0, totalSize: 0 },
  RUB: { baseSize: 0, marginSize: 0, totalSize: 0 }
}
2025-08-26T13:11:47.355Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:11:47.355Z bot:balancer positionIndex 1
2025-08-26T13:11:47.355Z bot:balancer position {
  pair: 'TPAY/RUB',
  base: 'TPAY',
  quote: 'RUB',
  figi: 'TCS00A108WX3',
  price: { units: 101, nano: 690000000 },
  priceNumber: 101.69,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 101, nano: 690000000 },
  lotPriceNumber: 101.69
}
2025-08-26T13:11:47.355Z bot:balancer Calculating how many rubles the desired share will be with multiplier
2025-08-26T13:11:47.355Z bot:balancer walletSumNumber 384.24
2025-08-26T13:11:47.355Z bot:balancer desiredPercent 16.759636226291143
2025-08-26T13:11:47.355Z bot:balancer desiredAmountNumber (considering multiplier) 257.58890494360435
2025-08-26T13:11:47.355Z bot:balancer Calculating how many lots can be bought before desired target
2025-08-26T13:11:47.355Z bot:balancer canBuyBeforeTargetLots 2
2025-08-26T13:11:47.355Z bot:balancer Calculating cost of position that can be bought before desired target
2025-08-26T13:11:47.355Z bot:balancer canBuyBeforeTargetNumber 203.38
2025-08-26T13:11:47.355Z bot:balancer Calculating difference between desired value and value before target. Unallocated remainder.
2025-08-26T13:11:47.355Z bot:balancer beforeDiffNumber 54.20890494360435
2025-08-26T13:11:47.355Z bot:balancer Summing up remainders
2025-08-26T13:11:47.355Z bot:balancer How much to buy (can be negative, then need to sell)
2025-08-26T13:11:47.355Z bot:balancer How many lots to buy (can be negative, then need to sell)
2025-08-26T13:11:47.355Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:11:47.356Z bot:balancer positionIndex 3
2025-08-26T13:11:47.356Z bot:balancer position {
  pair: 'TGLD@/RUB',
  base: 'TGLD@',
  quote: 'RUB',
  figi: 'TCS80A101X50',
  amount: 12,
  lotSize: 1,
  price: { units: 10, nano: 790000000 },
  priceNumber: 10.79,
  lotPrice: { units: 10, nano: 790000000 },
  totalPrice: { units: 129, nano: 4******** },
  totalPriceNumber: 129.48,
  lotPriceNumber: 10.79
}
2025-08-26T13:11:47.356Z bot:balancer Calculating how many rubles the desired share will be with multiplier
2025-08-26T13:11:47.356Z bot:balancer walletSumNumber 384.24
2025-08-26T13:11:47.356Z bot:balancer desiredPercent 8.799735645198863
2025-08-26T13:11:47.356Z bot:balancer desiredAmountNumber (considering multiplier) 135.24841697244844
2025-08-26T13:11:47.356Z bot:balancer Calculating how many lots can be bought before desired target
2025-08-26T13:11:47.356Z bot:balancer canBuyBeforeTargetLots 12
2025-08-26T13:11:47.356Z bot:balancer Calculating cost of position that can be bought before desired target
2025-08-26T13:11:47.356Z bot:balancer canBuyBeforeTargetNumber 129.48
2025-08-26T13:11:47.356Z bot:balancer Calculating difference between desired value and value before target. Unallocated remainder.
2025-08-26T13:11:47.356Z bot:balancer beforeDiffNumber 5.768416972448449
2025-08-26T13:11:47.356Z bot:balancer Summing up remainders
2025-08-26T13:11:47.356Z bot:balancer How much to buy (can be negative, then need to sell)
2025-08-26T13:11:47.356Z bot:balancer toBuyNumber 0
2025-08-26T13:11:47.356Z bot:balancer How many lots to buy (can be negative, then need to sell)
2025-08-26T13:11:47.356Z bot:balancer toBuyLots 0
2025-08-26T13:11:47.356Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:11:47.356Z bot:balancer positionIndex 7
2025-08-26T13:11:47.356Z bot:balancer position {
  pair: 'TRUR/RUB',
  base: 'TRUR',
  quote: 'RUB',
  figi: 'BBG000000001',
  amount: 14,
  lotSize: 1,
  price: { units: 9, nano: 300000000 },
  priceNumber: 9.3,
  lotPrice: { units: 9, nano: 300000000 },
  totalPrice: { units: 130, nano: ********* },
  totalPriceNumber: 130.2,
  lotPriceNumber: 9.3
}
2025-08-26T13:11:47.356Z bot:balancer Calculating how many rubles the desired share will be with multiplier
2025-08-26T13:11:47.356Z bot:balancer walletSumNumber 384.24
2025-08-26T13:11:47.356Z bot:balancer desiredPercent 8.874076494541905
2025-08-26T13:11:47.356Z bot:balancer desiredAmountNumber (considering multiplier) 136.39100609051127
2025-08-26T13:11:47.356Z bot:balancer Calculating how many lots can be bought before desired target
2025-08-26T13:11:47.356Z bot:balancer canBuyBeforeTargetLots 14
2025-08-26T13:11:47.356Z bot:balancer Calculating cost of position that can be bought before desired target
2025-08-26T13:11:47.356Z bot:balancer canBuyBeforeTargetNumber 130.*********00002
2025-08-26T13:11:47.356Z bot:balancer Calculating difference between desired value and value before target. Unallocated remainder.
2025-08-26T13:11:47.356Z bot:balancer beforeDiffNumber 6.191006090511252
2025-08-26T13:11:47.356Z bot:balancer Summing up remainders
2025-08-26T13:11:47.356Z bot:balancer How much to buy (can be negative, then need to sell)
2025-08-26T13:11:47.356Z bot:balancer toBuyNumber 2.842170943040401e-14
2025-08-26T13:11:47.356Z bot:balancer How many lots to buy (can be negative, then need to sell)
2025-08-26T13:11:47.356Z bot:balancer toBuyLots 0
2025-08-26T13:11:47.356Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:11:47.356Z bot:balancer positionIndex 6
2025-08-26T13:11:47.356Z bot:balancer position {
  pair: 'TRND/RUB',
  base: 'TRND',
  quote: 'RUB',
  figi: 'TCS00A10B0G9',
  amount: 24,
  lotSize: 1,
  price: { units: 9, nano: 890000000 },
  priceNumber: 9.89,
  lotPrice: { units: 9, nano: 890000000 },
  totalPrice: { units: 237, nano: 360000000 },
  totalPriceNumber: 237.36,
  lotPriceNumber: 9.89
}
2025-08-26T13:11:47.356Z bot:balancer Calculating how many rubles the desired share will be with multiplier
2025-08-26T13:11:47.356Z bot:balancer walletSumNumber 384.24
2025-08-26T13:11:47.356Z bot:balancer desiredPercent 15.989801389103645
2025-08-26T13:11:47.356Z bot:balancer desiredAmountNumber (considering multiplier) 245.75685142996738
2025-08-26T13:11:47.356Z bot:balancer Calculating how many lots can be bought before desired target
2025-08-26T13:11:47.356Z bot:balancer canBuyBeforeTargetLots 24
2025-08-26T13:11:47.356Z bot:balancer Calculating cost of position that can be bought before desired target
2025-08-26T13:11:47.356Z bot:balancer canBuyBeforeTargetNumber 237.36
2025-08-26T13:11:47.356Z bot:balancer Calculating difference between desired value and value before target. Unallocated remainder.
2025-08-26T13:11:47.356Z bot:balancer beforeDiffNumber 8.39685142996737
2025-08-26T13:11:47.356Z bot:balancer Summing up remainders
2025-08-26T13:11:47.356Z bot:balancer How much to buy (can be negative, then need to sell)
2025-08-26T13:11:47.356Z bot:balancer toBuyNumber 0
2025-08-26T13:11:47.356Z bot:balancer How many lots to buy (can be negative, then need to sell)
2025-08-26T13:11:47.356Z bot:balancer toBuyLots 0
2025-08-26T13:11:47.356Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:11:47.356Z bot:balancer positionIndex 8
2025-08-26T13:11:47.356Z bot:balancer position {
  pair: 'TBRU/RUB',
  base: 'TBRU',
  quote: 'RUB',
  figi: 'TCS60A1039N1',
  price: { units: 7, nano: 400000000 },
  priceNumber: 7.4,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 7, nano: 400000000 },
  lotPriceNumber: 7.4
}
2025-08-26T13:11:47.356Z bot:balancer Calculating how many rubles the desired share will be with multiplier
2025-08-26T13:11:47.356Z bot:balancer walletSumNumber 384.24
2025-08-26T13:11:47.356Z bot:balancer desiredPercent 6.560176451360741
2025-08-26T13:11:47.356Z bot:balancer desiredAmountNumber (considering multiplier) 100.82728798683405
2025-08-26T13:11:47.356Z bot:balancer Calculating how many lots can be bought before desired target
2025-08-26T13:11:47.356Z bot:balancer canBuyBeforeTargetLots 13
2025-08-26T13:11:47.356Z bot:balancer Calculating cost of position that can be bought before desired target
2025-08-26T13:11:47.356Z bot:balancer canBuyBeforeTargetNumber 96.2
2025-08-26T13:11:47.356Z bot:balancer Calculating difference between desired value and value before target. Unallocated remainder.
2025-08-26T13:11:47.356Z bot:balancer beforeDiffNumber 4.627287986834048
2025-08-26T13:11:47.356Z bot:balancer Summing up remainders
2025-08-26T13:11:47.356Z bot:balancer How much to buy (can be negative, then need to sell)
2025-08-26T13:11:47.356Z bot:balancer How many lots to buy (can be negative, then need to sell)
2025-08-26T13:11:47.356Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:11:47.356Z bot:balancer positionIndex 4
2025-08-26T13:11:47.356Z bot:balancer position {
  pair: 'TDIV@/RUB',
  base: 'TDIV@',
  quote: 'RUB',
  figi: 'TCS10A107563',
  amount: 12,
  lotSize: 1,
  price: { units: 10, nano: 550000000 },
  priceNumber: 10.55,
  lotPrice: { units: 10, nano: 550000000 },
  totalPrice: { units: 126, nano: ********* },
  totalPriceNumber: 126.6,
  lotPriceNumber: 10.55
}
2025-08-26T13:11:47.356Z bot:balancer Calculating how many rubles the desired share will be with multiplier
2025-08-26T13:11:47.356Z bot:balancer walletSumNumber 384.24
2025-08-26T13:11:47.356Z bot:balancer desiredPercent 8.895015973605236
2025-08-26T13:11:47.356Z bot:balancer desiredAmountNumber (considering multiplier) 136.71283750792304
2025-08-26T13:11:47.356Z bot:balancer Calculating how many lots can be bought before desired target
2025-08-26T13:11:47.356Z bot:balancer canBuyBeforeTargetLots 12
2025-08-26T13:11:47.356Z bot:balancer Calculating cost of position that can be bought before desired target
2025-08-26T13:11:47.356Z bot:balancer canBuyBeforeTargetNumber 126.*********00001
2025-08-26T13:11:47.356Z bot:balancer Calculating difference between desired value and value before target. Unallocated remainder.
2025-08-26T13:11:47.356Z bot:balancer beforeDiffNumber 10.112837507923032
2025-08-26T13:11:47.357Z bot:balancer Summing up remainders
2025-08-26T13:11:47.357Z bot:balancer How much to buy (can be negative, then need to sell)
2025-08-26T13:11:47.357Z bot:balancer toBuyNumber 1.4210854715202004e-14
2025-08-26T13:11:47.357Z bot:balancer How many lots to buy (can be negative, then need to sell)
2025-08-26T13:11:47.357Z bot:balancer toBuyLots 0
2025-08-26T13:11:47.357Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:11:47.357Z bot:balancer positionIndex 10
2025-08-26T13:11:47.357Z bot:balancer position {
  pair: 'TITR/RUB',
  base: 'TITR',
  quote: 'RUB',
  figi: 'TCS30A108BL2',
  price: { units: 6, nano: 660000000 },
  priceNumber: 6.66,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 6, nano: 660000000 },
  lotPriceNumber: 6.66
}
2025-08-26T13:11:47.357Z bot:balancer Calculating how many rubles the desired share will be with multiplier
2025-08-26T13:11:47.357Z bot:balancer walletSumNumber 384.24
2025-08-26T13:11:47.357Z bot:balancer desiredPercent 9.721340974058965
2025-08-26T13:11:47.357Z bot:balancer desiredAmountNumber (considering multiplier) 149.41312223489666
2025-08-26T13:11:47.357Z bot:balancer Calculating how many lots can be bought before desired target
2025-08-26T13:11:47.357Z bot:balancer canBuyBeforeTargetLots 22
2025-08-26T13:11:47.357Z bot:balancer Calculating cost of position that can be bought before desired target
2025-08-26T13:11:47.357Z bot:balancer canBuyBeforeTargetNumber 146.52
2025-08-26T13:11:47.357Z bot:balancer Calculating difference between desired value and value before target. Unallocated remainder.
2025-08-26T13:11:47.357Z bot:balancer beforeDiffNumber 2.8931222348966514
2025-08-26T13:11:47.357Z bot:balancer Summing up remainders
2025-08-26T13:11:47.357Z bot:balancer How much to buy (can be negative, then need to sell)
2025-08-26T13:11:47.357Z bot:balancer How many lots to buy (can be negative, then need to sell)
2025-08-26T13:11:47.357Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:11:47.357Z bot:balancer positionIndex 5
2025-08-26T13:11:47.357Z bot:balancer position {
  pair: 'TLCB/RUB',
  base: 'TLCB',
  quote: 'RUB',
  figi: 'TCS20A107597',
  price: { units: 10, nano: 150000000 },
  priceNumber: 10.15,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 10, nano: 150000000 },
  lotPriceNumber: 10.15
}
2025-08-26T13:11:47.357Z bot:balancer Calculating how many rubles the desired share will be with multiplier
2025-08-26T13:11:47.357Z bot:balancer walletSumNumber 384.24
2025-08-26T13:11:47.357Z bot:balancer desiredPercent 8.43784405353226
2025-08-26T13:11:47.357Z bot:balancer desiredAmountNumber (considering multiplier) 129.6862879651694
2025-08-26T13:11:47.357Z bot:balancer Calculating how many lots can be bought before desired target
2025-08-26T13:11:47.357Z bot:balancer canBuyBeforeTargetLots 12
2025-08-26T13:11:47.357Z bot:balancer Calculating cost of position that can be bought before desired target
2025-08-26T13:11:47.357Z bot:balancer canBuyBeforeTargetNumber 121.********000001
2025-08-26T13:11:47.357Z bot:balancer Calculating difference between desired value and value before target. Unallocated remainder.
2025-08-26T13:11:47.357Z bot:balancer beforeDiffNumber 7.886287965169402
2025-08-26T13:11:47.357Z bot:balancer Summing up remainders
2025-08-26T13:11:47.357Z bot:balancer How much to buy (can be negative, then need to sell)
2025-08-26T13:11:47.357Z bot:balancer How many lots to buy (can be negative, then need to sell)
2025-08-26T13:11:47.357Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:11:47.357Z bot:balancer positionIndex 0
2025-08-26T13:11:47.357Z bot:balancer position {
  pair: 'TMON/RUB',
  base: 'TMON',
  quote: 'RUB',
  figi: 'TCS70A106DL2',
  price: { units: 141, nano: ********* },
  priceNumber: 141.7,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 141, nano: ********* },
  lotPriceNumber: 141.7
}
2025-08-26T13:11:47.357Z bot:balancer Calculating how many rubles the desired share will be with multiplier
2025-08-26T13:11:47.357Z bot:balancer walletSumNumber 384.24
2025-08-26T13:11:47.357Z bot:balancer desiredPercent 7.6700052377814005
2025-08-26T13:11:47.357Z bot:balancer desiredAmountNumber (considering multiplier) 117.88491250260502
2025-08-26T13:11:47.357Z bot:balancer Calculating how many lots can be bought before desired target
2025-08-26T13:11:47.357Z bot:balancer canBuyBeforeTargetLots 0
2025-08-26T13:11:47.357Z bot:balancer Calculating cost of position that can be bought before desired target
2025-08-26T13:11:47.357Z bot:balancer canBuyBeforeTargetNumber 0
2025-08-26T13:11:47.357Z bot:balancer Calculating difference between desired value and value before target. Unallocated remainder.
2025-08-26T13:11:47.357Z bot:balancer beforeDiffNumber 117.88491250260502
2025-08-26T13:11:47.357Z bot:balancer Summing up remainders
2025-08-26T13:11:47.357Z bot:balancer How much to buy (can be negative, then need to sell)
2025-08-26T13:11:47.357Z bot:balancer How many lots to buy (can be negative, then need to sell)
2025-08-26T13:11:47.357Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:11:47.357Z bot:balancer positionIndex 9
2025-08-26T13:11:47.357Z bot:balancer position {
  pair: 'TMOS@/RUB',
  base: 'TMOS@',
  quote: 'RUB',
  figi: 'TCS60A101X76',
  amount: 18,
  lotSize: 1,
  price: { units: 6, nano: ********* },
  priceNumber: 6.7,
  lotPrice: { units: 6, nano: ********* },
  totalPrice: { units: 120, nano: ********* },
  totalPriceNumber: 120.6,
  lotPriceNumber: 6.7
}
2025-08-26T13:11:47.357Z bot:balancer Calculating how many rubles the desired share will be with multiplier
2025-08-26T13:11:47.357Z bot:balancer walletSumNumber 384.24
2025-08-26T13:11:47.357Z bot:balancer desiredPercent 8.292367554525832
2025-08-26T13:11:47.357Z bot:balancer desiredAmountNumber (considering multiplier) 127.**************
2025-08-26T13:11:47.357Z bot:balancer Calculating how many lots can be bought before desired target
2025-08-26T13:11:47.357Z bot:balancer canBuyBeforeTargetLots 19
2025-08-26T13:11:47.357Z bot:balancer Calculating cost of position that can be bought before desired target
2025-08-26T13:11:47.357Z bot:balancer canBuyBeforeTargetNumber 127.3
2025-08-26T13:11:47.358Z bot:balancer Calculating difference between desired value and value before target. Unallocated remainder.
2025-08-26T13:11:47.358Z bot:balancer beforeDiffNumber 0.*****************
2025-08-26T13:11:47.358Z bot:balancer Summing up remainders
2025-08-26T13:11:47.358Z bot:balancer How much to buy (can be negative, then need to sell)
2025-08-26T13:11:47.358Z bot:balancer toBuyNumber 6.7000000********
2025-08-26T13:11:47.358Z bot:balancer How many lots to buy (can be negative, then need to sell)
2025-08-26T13:11:47.358Z bot:balancer toBuyLots 1
2025-08-26T13:11:47.358Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:11:47.358Z bot:balancer positionIndex 2
2025-08-26T13:11:47.358Z bot:balancer position {
  pair: 'TOFZ/RUB',
  base: 'TOFZ',
  quote: 'RUB',
  figi: 'TCS70A10A1L8',
  price: { units: 13, nano: 290000000 },
  priceNumber: 13.29,
  amount: 0,
  lotSize: 1,
  lotPrice: { units: 13, nano: 290000000 },
  lotPriceNumber: 13.29
}
2025-08-26T13:11:47.358Z bot:balancer Calculating how many rubles the desired share will be with multiplier
2025-08-26T13:11:47.358Z bot:balancer walletSumNumber 384.24
2025-08-26T13:11:47.358Z bot:balancer desiredPercent 0
2025-08-26T13:11:47.358Z bot:balancer desiredAmountNumber (considering multiplier) 0
2025-08-26T13:11:47.358Z bot:balancer Calculating how many lots can be bought before desired target
2025-08-26T13:11:47.358Z bot:balancer canBuyBeforeTargetLots 0
2025-08-26T13:11:47.358Z bot:balancer Calculating cost of position that can be bought before desired target
2025-08-26T13:11:47.358Z bot:balancer canBuyBeforeTargetNumber 0
2025-08-26T13:11:47.358Z bot:balancer Calculating difference between desired value and value before target. Unallocated remainder.
2025-08-26T13:11:47.358Z bot:balancer beforeDiffNumber 0
2025-08-26T13:11:47.358Z bot:balancer Summing up remainders
2025-08-26T13:11:47.358Z bot:balancer How much to buy (can be negative, then need to sell)
2025-08-26T13:11:47.358Z bot:balancer How many lots to buy (can be negative, then need to sell)
2025-08-26T13:11:47.358Z bot:balancer  Looking for base (ticker) in wallet
2025-08-26T13:11:47.358Z bot:balancer positionIndex 11
2025-08-26T13:11:47.358Z bot:balancer position {
  pair: 'RUB/RUB',
  base: 'RUB',
  quote: 'RUB',
  figi: undefined,
  amount: -360,
  lotSize: 1,
  price: { units: 1, nano: 0 },
  priceNumber: 1,
  lotPrice: { units: 1, nano: 0 },
  totalPrice: { units: -360, nano: 0 },
  lotPriceNumber: 1,
  totalPriceNumber: -360
}
2025-08-26T13:11:47.358Z bot:balancer Calculating how many rubles the desired share will be with multiplier
2025-08-26T13:11:47.358Z bot:balancer walletSumNumber 384.24
2025-08-26T13:11:47.358Z bot:balancer desiredPercent 0
2025-08-26T13:11:47.358Z bot:balancer desiredAmountNumber (considering multiplier) 0
2025-08-26T13:11:47.358Z bot:balancer Calculating how many lots can be bought before desired target
2025-08-26T13:11:47.358Z bot:balancer canBuyBeforeTargetLots 0
2025-08-26T13:11:47.358Z bot:balancer Calculating cost of position that can be bought before desired target
2025-08-26T13:11:47.358Z bot:balancer canBuyBeforeTargetNumber 0
2025-08-26T13:11:47.358Z bot:balancer Calculating difference between desired value and value before target. Unallocated remainder.
2025-08-26T13:11:47.358Z bot:balancer beforeDiffNumber 0
2025-08-26T13:11:47.358Z bot:balancer Summing up remainders
2025-08-26T13:11:47.358Z bot:balancer How much to buy (can be negative, then need to sell)
2025-08-26T13:11:47.358Z bot:balancer toBuyNumber 360
2025-08-26T13:11:47.358Z bot:balancer How many lots to buy (can be negative, then need to sell)
2025-08-26T13:11:47.358Z bot:balancer toBuyLots 360
2025-08-26T13:11:47.358Z bot:balancer sortedWallet [
  {
    pair: 'TMON/RUB',
    base: 'TMON',
    quote: 'RUB',
    figi: 'TCS70A106DL2',
    price: { units: 141, nano: ********* },
    priceNumber: 141.7,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 141, nano: ********* },
    lotPriceNumber: 141.7,
    desiredAmountNumber: 117.88491250260502,
    canBuyBeforeTargetLots: 0,
    canBuyBeforeTargetNumber: 0,
    beforeDiffNumber: 117.88491250260502
  },
  {
    pair: 'TPAY/RUB',
    base: 'TPAY',
    quote: 'RUB',
    figi: 'TCS00A108WX3',
    price: { units: 101, nano: 690000000 },
    priceNumber: 101.69,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 101, nano: 690000000 },
    lotPriceNumber: 101.69,
    desiredAmountNumber: 257.58890494360435,
    canBuyBeforeTargetLots: 2,
    canBuyBeforeTargetNumber: 203.38,
    beforeDiffNumber: 54.20890494360435
  },
  {
    pair: 'TOFZ/RUB',
    base: 'TOFZ',
    quote: 'RUB',
    figi: 'TCS70A10A1L8',
    price: { units: 13, nano: 290000000 },
    priceNumber: 13.29,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 13, nano: 290000000 },
    lotPriceNumber: 13.29,
    desiredAmountNumber: 0,
    canBuyBeforeTargetLots: 0,
    canBuyBeforeTargetNumber: 0,
    beforeDiffNumber: 0
  },
  {
    pair: 'TGLD@/RUB',
    base: 'TGLD@',
    quote: 'RUB',
    figi: 'TCS80A101X50',
    amount: 12,
    lotSize: 1,
    price: { units: 10, nano: 790000000 },
    priceNumber: 10.79,
    lotPrice: { units: 10, nano: 790000000 },
    totalPrice: { units: 129, nano: 4******** },
    totalPriceNumber: 129.48,
    lotPriceNumber: 10.79,
    desiredAmountNumber: 135.24841697244844,
    canBuyBeforeTargetLots: 12,
    canBuyBeforeTargetNumber: 129.48,
    beforeDiffNumber: 5.768416972448449,
    toBuyNumber: 0,
    toBuyLots: 0
  },
  {
    pair: 'TDIV@/RUB',
    base: 'TDIV@',
    quote: 'RUB',
    figi: 'TCS10A107563',
    amount: 12,
    lotSize: 1,
    price: { units: 10, nano: 550000000 },
    priceNumber: 10.55,
    lotPrice: { units: 10, nano: 550000000 },
    totalPrice: { units: 126, nano: ********* },
    totalPriceNumber: 126.6,
    lotPriceNumber: 10.55,
    desiredAmountNumber: 136.71283750792304,
    canBuyBeforeTargetLots: 12,
    canBuyBeforeTargetNumber: 126.*********00001,
    beforeDiffNumber: 10.112837507923032,
    toBuyNumber: 1.4210854715202004e-14,
    toBuyLots: 0
  },
  {
    pair: 'TLCB/RUB',
    base: 'TLCB',
    quote: 'RUB',
    figi: 'TCS20A107597',
    price: { units: 10, nano: 150000000 },
    priceNumber: 10.15,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 10, nano: 150000000 },
    lotPriceNumber: 10.15,
    desiredAmountNumber: 129.6862879651694,
    canBuyBeforeTargetLots: 12,
    canBuyBeforeTargetNumber: 121.********000001,
    beforeDiffNumber: 7.886287965169402
  },
  {
    pair: 'TRND/RUB',
    base: 'TRND',
    quote: 'RUB',
    figi: 'TCS00A10B0G9',
    amount: 24,
    lotSize: 1,
    price: { units: 9, nano: 890000000 },
    priceNumber: 9.89,
    lotPrice: { units: 9, nano: 890000000 },
    totalPrice: { units: 237, nano: 360000000 },
    totalPriceNumber: 237.36,
    lotPriceNumber: 9.89,
    desiredAmountNumber: 245.75685142996738,
    canBuyBeforeTargetLots: 24,
    canBuyBeforeTargetNumber: 237.36,
    beforeDiffNumber: 8.39685142996737,
    toBuyNumber: 0,
    toBuyLots: 0
  },
  {
    pair: 'TRUR/RUB',
    base: 'TRUR',
    quote: 'RUB',
    figi: 'BBG000000001',
    amount: 14,
    lotSize: 1,
    price: { units: 9, nano: 300000000 },
    priceNumber: 9.3,
    lotPrice: { units: 9, nano: 300000000 },
    totalPrice: { units: 130, nano: ********* },
    totalPriceNumber: 130.2,
    lotPriceNumber: 9.3,
    desiredAmountNumber: 136.39100609051127,
    canBuyBeforeTargetLots: 14,
    canBuyBeforeTargetNumber: 130.*********00002,
    beforeDiffNumber: 6.191006090511252,
    toBuyNumber: 2.842170943040401e-14,
    toBuyLots: 0
  },
  {
    pair: 'TBRU/RUB',
    base: 'TBRU',
    quote: 'RUB',
    figi: 'TCS60A1039N1',
    price: { units: 7, nano: 400000000 },
    priceNumber: 7.4,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 7, nano: 400000000 },
    lotPriceNumber: 7.4,
    desiredAmountNumber: 100.82728798683405,
    canBuyBeforeTargetLots: 13,
    canBuyBeforeTargetNumber: 96.2,
    beforeDiffNumber: 4.627287986834048
  },
  {
    pair: 'TMOS@/RUB',
    base: 'TMOS@',
    quote: 'RUB',
    figi: 'TCS60A101X76',
    amount: 18,
    lotSize: 1,
    price: { units: 6, nano: ********* },
    priceNumber: 6.7,
    lotPrice: { units: 6, nano: ********* },
    totalPrice: { units: 120, nano: ********* },
    totalPriceNumber: 120.6,
    lotPriceNumber: 6.7,
    desiredAmountNumber: 127.**************,
    canBuyBeforeTargetLots: 19,
    canBuyBeforeTargetNumber: 127.3,
    beforeDiffNumber: 0.*****************,
    toBuyNumber: 6.7000000********,
    toBuyLots: 1
  },
  {
    pair: 'TITR/RUB',
    base: 'TITR',
    quote: 'RUB',
    figi: 'TCS30A108BL2',
    price: { units: 6, nano: 660000000 },
    priceNumber: 6.66,
    amount: 0,
    lotSize: 1,
    lotPrice: { units: 6, nano: 660000000 },
    lotPriceNumber: 6.66,
    desiredAmountNumber: 149.41312223489666,
    canBuyBeforeTargetLots: 22,
    canBuyBeforeTargetNumber: 146.52,
    beforeDiffNumber: 2.8931222348966514
  },
  {
    pair: 'RUB/RUB',
    base: 'RUB',
    quote: 'RUB',
    figi: undefined,
    amount: -360,
    lotSize: 1,
    price: { units: 1, nano: 0 },
    priceNumber: 1,
    lotPrice: { units: 1, nano: 0 },
    totalPrice: { units: -360, nano: 0 },
    lotPriceNumber: 1,
    totalPriceNumber: -360,
    desiredAmountNumber: 0,
    canBuyBeforeTargetLots: 0,
    canBuyBeforeTargetNumber: 0,
    beforeDiffNumber: 0,
    toBuyNumber: 360,
    toBuyLots: 360
  }
]
2025-08-26T13:11:47.359Z bot:balancer ordersPlanned [
  {
    pair: 'TMOS@/RUB',
    base: 'TMOS@',
    quote: 'RUB',
    figi: 'TCS60A101X76',
    amount: 18,
    lotSize: 1,
    price: { units: 6, nano: ********* },
    priceNumber: 6.7,
    lotPrice: { units: 6, nano: ********* },
    totalPrice: { units: 120, nano: ********* },
    totalPriceNumber: 120.6,
    lotPriceNumber: 6.7,
    desiredAmountNumber: 127.**************,
    canBuyBeforeTargetLots: 19,
    canBuyBeforeTargetNumber: 127.3,
    beforeDiffNumber: 0.*****************,
    toBuyNumber: 6.7000000********,
    toBuyLots: 1
  },
  {
    pair: 'RUB/RUB',
    base: 'RUB',
    quote: 'RUB',
    figi: undefined,
    amount: -360,
    lotSize: 1,
    price: { units: 1, nano: 0 },
    priceNumber: 1,
    lotPrice: { units: 1, nano: 0 },
    totalPrice: { units: -360, nano: 0 },
    lotPriceNumber: 1,
    totalPriceNumber: -360,
    desiredAmountNumber: 0,
    canBuyBeforeTargetLots: 0,
    canBuyBeforeTargetNumber: 0,
    beforeDiffNumber: 0,
    toBuyNumber: 360,
    toBuyLots: 360
  }
]
2025-08-26T13:11:47.360Z bot:balancer walletInfo { remains: 218.11999999999983 }
2025-08-26T13:11:47.360Z bot:balancer Creating necessary orders for all positions
2025-08-26T13:11:47.360Z bot:provider generateOrders
2025-08-26T13:11:47.361Z bot:provider generateOrder
2025-08-26T13:11:47.361Z bot:provider position {
  pair: 'TMOS@/RUB',
  base: 'TMOS@',
  quote: 'RUB',
  figi: 'TCS60A101X76',
  amount: 18,
  lotSize: 1,
  price: { units: 6, nano: ********* },
  priceNumber: 6.7,
  lotPrice: { units: 6, nano: ********* },
  totalPrice: { units: 120, nano: ********* },
  totalPriceNumber: 120.6,
  lotPriceNumber: 6.7,
  desiredAmountNumber: 127.**************,
  canBuyBeforeTargetLots: 19,
  canBuyBeforeTargetNumber: 127.3,
  beforeDiffNumber: 0.*****************,
  toBuyNumber: 6.7000000********,
  toBuyLots: 1
}
2025-08-26T13:11:47.361Z bot:provider Position is not currency
2025-08-26T13:11:47.361Z bot:provider position.toBuyLots 1
2025-08-26T13:11:47.361Z bot:provider Position is greater than or equal to 1 lot
2025-08-26T13:11:47.361Z bot:provider direction 1
2025-08-26T13:11:47.361Z bot:provider position {
  pair: 'TMOS@/RUB',
  base: 'TMOS@',
  quote: 'RUB',
  figi: 'TCS60A101X76',
  amount: 18,
  lotSize: 1,
  price: { units: 6, nano: ********* },
  priceNumber: 6.7,
  lotPrice: { units: 6, nano: ********* },
  totalPrice: { units: 120, nano: ********* },
  totalPriceNumber: 120.6,
  lotPriceNumber: 6.7,
  desiredAmountNumber: 127.**************,
  canBuyBeforeTargetLots: 19,
  canBuyBeforeTargetNumber: 127.3,
  beforeDiffNumber: 0.*****************,
  toBuyNumber: 6.7000000********,
  toBuyLots: 1
}
2025-08-26T13:11:47.361Z bot:provider Creating market order
2025-08-26T13:11:47.361Z bot:provider Sending market order {
  accountId: '**********',
  figi: 'TCS60A101X76',
  quantity: 1,
  direction: 1,
  orderType: 2,
  orderId: '3s32k1ezimeskezky'
}
2025-08-26T13:11:47.888Z bot:provider Successfully placed order {
  orderId: '************',
  executionReportStatus: 1,
  lotsRequested: 1,
  lotsExecuted: 1,
  initialOrderPrice: { currency: 'rub', units: 6, nano: ********* },
  executedOrderPrice: { currency: 'rub', units: 6, nano: ********* },
  totalOrderAmount: { currency: 'rub', units: 6, nano: ********* },
  initialCommission: { currency: 'rub', units: 0, nano: 0 },
  executedCommission: { currency: 'rub', units: 0, nano: 0 },
  aciValue: undefined,
  figi: 'TCS60A101X76',
  direction: 1,
  initialSecurityPrice: { currency: 'rub', units: 6, nano: ********* },
  orderType: 2,
  message: '',
  initialOrderPricePt: undefined,
  instrumentUid: 'f509af83-6e71-462f-901f-bcb073f6773b',
  orderRequestId: '3s32k1ezimeskezky',
  responseMetadata: {
    trackingId: '0dbdfbc9560ba4755747fb42952aefb0',
    serverTime: 2025-08-26T13:11:47.852Z
  }
}
2025-08-26T13:11:52.890Z bot:provider generateOrder
2025-08-26T13:11:52.890Z bot:provider position {
  pair: 'RUB/RUB',
  base: 'RUB',
  quote: 'RUB',
  figi: undefined,
  amount: -360,
  lotSize: 1,
  price: { units: 1, nano: 0 },
  priceNumber: 1,
  lotPrice: { units: 1, nano: 0 },
  totalPrice: { units: -360, nano: 0 },
  lotPriceNumber: 1,
  totalPriceNumber: -360,
  desiredAmountNumber: 0,
  canBuyBeforeTargetLots: 0,
  canBuyBeforeTargetNumber: 0,
  beforeDiffNumber: 0,
  toBuyNumber: 360,
  toBuyLots: 360
}
2025-08-26T13:11:52.891Z bot:provider If position is RUB, do nothing
BALANCING RESULT:
Format: TICKER: diff: before% -> after% (target%)
Where: before% = current share, after% = actual share after balancing, (target%) = target from balancer, diff = change in percentage points

TRND: +0.02%: 31.87% -> 31.89% (31.61%)
TRUR: -0.01%: 17.50% -> 17.49% (17.34%)
TGLD: -0.01%: 17.40% -> 17.40% (17.24%)
TDIV: -0.01%: 17.02% -> 17.01% (16.86%)
TMOS: -0.01%: 16.21% -> 16.20% (16.95%)
TMON: 0%: 0.00% -> 0.00% (0.00%)
TPAY: 0%: 0.00% -> 0.00% (0.00%)
TOFZ: 0%: 0.00% -> 0.00% (0.00%)
TLCB: 0%: 0.00% -> 0.00% (0.00%)
TBRU: 0%: 0.00% -> 0.00% (0.00%)
TITR: 0%: 0.00% -> 0.00% (0.00%)
RUR: -360.00 RUB
2025-08-26T13:11:52.897Z bot:provider ITERATION #1 FINISHED. TIME: Tue Aug 26 2025 16:11:52 GMT+0300 (Moscow Standard Time)
