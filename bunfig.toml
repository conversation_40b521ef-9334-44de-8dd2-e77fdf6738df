[install]
# Использовать bun registry для оптимальной производительности
registry = "https://registry.bun.sh/"

[install.scopes]
# Настройки для различных scope'ов если понадобятся

[test]
# Настройки для тестирования
preload = ["./src/test-setup.ts"]
timeout = 10000

[run]
# Настройки для выполнения скриптов
bun = true

[install.cache]
# Настройки кэширования
dir = ".bun"

[install.lockfile]
# Использовать bun.lockb
format = "bun"

[build]
# Настройки сборки для оптимизации
target = "bun"
minify = true
sourcemap = true
splitting = true
