# Маржинальная торговля в ETF Balancer Bot

## Обзор

Система маржинальной торговли позволяет увеличить размер портфеля за счет заемных средств с настраиваемым множителем (максимум x4).

## Основные возможности

### 1. Множитель портфеля
- **Настройка**: `MARGIN_MULTIPLIER` в `src/config.ts`
- **Диапазон**: 1-4 (где 1 = без маржи, 2 = x2, 3 = x3, 4 = x4)
- **По умолчанию**: 2 (x2)

### 2. Бесплатный перенос позиций
- **Порог**: `FREE_MARGIN_THRESHOLD` в `src/config.ts`
- **По умолчанию**: 5,000 руб
- **Логика**: Позиции до 5,000 руб переносятся бесплатно

### 3. Стратегии управления маржей
- **`remove`**: Убирать маржу в конце торгового дня
- **`keep`**: Оставлять маржу всегда
- **`keep_if_small`**: Оставлять только если сумма маржинальных позиций < порога

## Конфигурация

### Основные параметры

- **Включение/выключение**: `MARGIN_TRADING_ENABLED` в `src/config.ts`
- **По умолчанию**: `true` (включено)
- **Логика**: Если `false`, маржинальная торговля полностью отключается, стратегии не применяются

### Параметры маржинальной торговли

- **Множитель**: `MARGIN_MULTIPLIER` в `src/config.ts`
- **По умолчанию**: 4 (4x плечо)
- **Логика**: Портфель может быть увеличен в 4 раза за счет маржи

- **Порог**: `FREE_MARGIN_THRESHOLD` в `src/config.ts`
- **По умолчанию**: 5,000 руб
- **Логика**: Позиции до 5,000 руб переносятся бесплатно

### 3. Стратегии управления маржей
- **`remove`**: Убирать маржу в конце торгового дня
- **`keep`**: Оставлять маржу всегда
- **`keep_if_small`**: Оставлять только если сумма маржинальных позиций < порога

**Примечание**: Стратегии применяются только если `MARGIN_TRADING_ENABLED = true`

```typescript
// src/config.ts
export const MARGIN_TRADING_ENABLED: boolean = true; // Включить/выключить маржинальную торговлю
export const MARGIN_MULTIPLIER: number = 4; // Множитель портфеля (1-4)
export const FREE_MARGIN_THRESHOLD: number = 5000; // Порог бесплатного переноса в рублях
export const MARGIN_BALANCING_STRATEGY: 'remove' | 'keep' | 'keep_if_small' = 'keep_if_small';
```

**Важно**: Время применения стратегии определяется **динамически** во время балансировки, а не через статическую настройку. Система автоматически вычисляет:

1. **Время до закрытия рынка** - через API или настройки
2. **Время до следующей балансировки** - на основе `BALANCE_INTERVAL`
3. **Является ли это последней балансировкой дня**

## Умная логика времени

### Когда применяется стратегия маржи

Стратегия автоматически применяется если:

- **До закрытия рынка меньше времени до следующей балансировки**
- **Или до закрытия рынка меньше 15 минут** (последняя балансировка дня)

### Примеры

```
Время: 9:00, Закрытие: 18:45, Балансировка: каждый час
→ До закрытия: 9ч 45мин, До следующей балансировки: 1ч
→ Стратегия НЕ применяется (есть время для следующей балансировки)

Время: 18:30, Закрытие: 18:45, Балансировка: каждый час  
→ До закрытия: 15мин, До следующей балансировки: 1ч
→ Стратегия применяется (последняя балансировка дня)

Время: 19:00, Закрытие: 18:45, Балансировка: каждый час
→ Рынок закрыт
→ Стратегия применяется (конец торгового дня)
```

## Архитектура

### Основные компоненты

1. **MarginCalculator** (`src/utils/marginCalculator.ts`)
   - Расчет доступной маржи
   - Проверка лимитов
   - Расчет стоимости переноса
   - Применение стратегий

2. **Интеграция в балансировщик** (`src/balancer/index.ts`)
   - Автоматическое определение маржинальных позиций
   - Применение стратегий управления
   - Расчет оптимальных размеров с учетом множителя

3. **Типы** (`src/types.d.ts`)
   - `MarginPosition` - расширенная позиция с маржинальными данными
   - `MarginBalancingStrategy` - стратегии управления
   - `MarginConfig` - конфигурация системы

## Примеры использования

### Базовый расчет маржи

```typescript
import { MarginCalculator } from './src/utils/marginCalculator';

const config = {
  multiplier: 2,
  freeThreshold: 5000,
  strategy: 'keep_if_small',
  lastBalanceTime: '18:45'
};

const calculator = new MarginCalculator(config);

// Расчет доступной маржи для портфеля стоимостью 1,000,000 руб
const portfolio = [{ totalPriceNumber: 1000000 }];
const availableMargin = calculator.calculateAvailableMargin(portfolio);
// Результат: 1,000,000 руб (100% от портфеля для множителя x2)
```

### Проверка лимитов

```typescript
const limits = calculator.checkMarginLimits(portfolio, marginPositions);
console.log(`Уровень риска: ${limits.riskLevel}`);
console.log(`Оставшаяся маржа: ${limits.remainingMargin} руб`);
```

### Применение стратегии

```typescript
const strategy = calculator.applyMarginStrategy(
  marginPositions, 
  'keep_if_small', 
  new Date(),
  60000 * 60, // балансировка каждый час
  '18:45'     // время закрытия рынка
);

if (strategy.shouldRemoveMargin) {
  console.log(`Убираем маржу: ${strategy.reason}`);
  console.log(`Стоимость переноса: ${strategy.transferCost} руб`);
  console.log(`До закрытия рынка: ${strategy.timeInfo.timeToClose} мин`);
  console.log(`Последняя балансировка дня: ${strategy.timeInfo.isLastBalance}`);
}
```

### Получение информации о времени

```typescript
// Проверка, нужно ли применять стратегию
const shouldApply = calculator.shouldApplyMarginStrategy(
  new Date(),
  60000 * 60,  // балансировка каждый час
  '18:45'      // закрытие рынка
);

// Получение детальной информации о времени
const timeInfo = calculator.shouldApplyMarginStrategy(
  new Date(),
  60000 * 60,
  '18:45'
);
```

## Тестирование

Запуск тестов маржинальной торговли:

```bash
bun run test:margin
```

Тест проверяет:
- Расчет доступной маржи
- Проверку лимитов
- Стоимость переноса позиций
- Применение стратегий
- Расчет оптимальных размеров позиций

## Логика работы

### 1. Определение маржинальных позиций
- Позиция считается маржинальной, если её стоимость превышает базовую стоимость
- Базовая стоимость = Общая стоимость / Множитель

### 2. Расчет доступной маржи
- Доступная маржа = Общая стоимость портфеля × (Множитель - 1)
- Пример: при множителе x2 и портфеле 1,000,000 руб → доступная маржа 1,000,000 руб

### 3. Стратегии управления
- **Время применения**: только в последней балансировке торгового дня
- **Автоматическое решение**: на основе настроенной стратегии
- **Логирование**: все решения и их причины записываются в лог

### 4. Оптимизация позиций
- Базовый размер = Целевая доля × Общая стоимость портфеля
- Маржинальный размер = Минимум(Доступная маржа × Доля, Базовый размер × (Множитель - 1))
- Общий размер = Базовый размер + Маржинальный размер

## Безопасность

### Контроль рисков
- **Уровень риска**: автоматическое определение (low/medium/high)
- **Лимиты**: проверка превышения доступной маржи
- **Маржин-колл**: мониторинг риска принудительного закрытия

### Валидация
- Проверка корректности множителя (1-4)
- Валидация порога бесплатного переноса
- Контроль времени применения стратегий

## Мониторинг

### Логи
- Все операции с маржей записываются в лог
- Детализация решений по стратегиям
- Стоимость переноса позиций

### Метрики
- Доступная и использованная маржа
- Уровень риска портфеля
- Эффективность стратегий управления

## Расширение функциональности

### Возможные улучшения
1. **Динамические множители** на основе волатильности
2. **Интеграция с риск-менеджментом** для автоматической корректировки
3. **Аналитика эффективности** маржинальной торговли
4. **Уведомления** о критических уровнях риска

### Добавление новых стратегий
```typescript
// В src/utils/marginCalculator.ts
case 'custom_strategy':
  // Ваша логика
  break;
```

## Заключение

Система маржинальной торговли обеспечивает гибкое управление портфелем с автоматическим контролем рисков и настраиваемыми стратегиями. Все настройки доступны через конфигурационный файл, а логика полностью интегрирована в основной балансировщик.
