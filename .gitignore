# See https://help.github.com/ignore-files/ for more about ignoring files.

# private
CONFIG.json
/private/
/.tmp/
/.data
tasks/
news_meta/
news/
etf_metrics/

# dependencies
.bun/
node_modules/
/invest-api/node_modules/

# testing
coverage/

# production
build/

# false folder when use bun install --no-cache
false/

# misc
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.production.local




.vscode
*.snap
*.xlog
.rocks/
.rocks/
.rocks
__pycache__
temp/
tmp/

