# Детальный вывод результатов балансировки

## Описание

Реализован детальный вывод результатов балансировки портфеля в удобочитаемом формате, показывающий изменения долей каждого инструмента до и после балансировки. Старый JSON формат полностью заменен на читаемый список с подсказками.

## Формат вывода

Вместо простого JSON теперь выводится детальная информация с diff и точностью до 2 знаков:

```
BALANCING RESULT:
Format: TICKER: diff: before% -> after% (target%)
Where: before% = current share, after% = actual share after balancing, (target%) = target from balancer, diff = change in percentage points

TPAY: +1.00%: 18.00% -> 19.00% (19.00%)
TGLD: +1.00%: 16.00% -> 17.00% (17.00%)
TRND: +1.00%: 14.00% -> 15.00% (15.00%)
TRUR: +1.00%: 13.00% -> 14.00% (14.00%)
TMOS: +1.00%: 13.00% -> 14.00% (14.00%)
TDIV: +1.00%: 12.00% -> 13.00% (13.00%)
TOFZ: +1.00%: 7.00% -> 8.00% (8.00%)
TMON: 0%: 0.00% -> 0.00% (0.00%)
TLCB: 0%: 0.00% -> 0.00% (0.00%)
TBRU: 0%: 0.00% -> 0.00% (0.00%)
TITR: 0%: 0.00% -> 0.00% (0.00%)
RUR: 5000.00 RUB

### Расшифровка формата

- **TICKER**: Тикер инструмента
- **diff**: Изменение в процентных пунктах (с точностью до 2 знаков)
- **before%**: Доля в портфеле ДО балансировки (с точностью до 2 знаков)
- **after%**: Фактическая доля ПОСЛЕ балансировки (с точностью до 2 знаков)
- **(target%)**: Целевая доля, рассчитанная балансировщиком (с точностью до 2 знаков)
- **RUR**: Баланс рублей (может быть отрицательным при маржинальной торговле)

## Как это работает

1. **До балансировки**: Система запрашивает текущий баланс портфеля и рассчитывает доли каждого инструмента
2. **Балансировка**: Выполняется стандартная логика балансировки, генерируются ордера
3. **После балансировки**: Система снова запрашивает баланс и рассчитывает новые доли
4. **Вывод**: Формируется детальный отчет с сравнением всех значений

## Преимущества

- **Прозрачность**: Видно, как изменился портфель
- **Отладка**: Легко понять, что пошло не так
- **Мониторинг**: Можно отслеживать эффективность балансировки
- **Понятность**: Подсказка формата сверху вывода

## Использование

### Автоматически
При каждом запуске балансировщика детальный вывод появляется автоматически.

### Демонстрация
Для демонстрации функциональности:

```bash
bun run demo:detailed-output
```

### Тестирование
Для проверки логики расчета долей:

```bash
bun run test:utils
```

## Технические детали

### Функция расчета долей
```typescript
const calculatePortfolioShares = (wallet: Wallet): Record<string, number> => {
  // Исключаем валюты (позиции где base === quote)
  const securities = wallet.filter(p => p.base !== p.quote);
  const totalValue = _.sumBy(securities, 'totalPriceNumber');
  
  if (totalValue <= 0) return {};
  
  const shares: Record<string, number> = {};
  for (const position of securities) {
    if (position.base && position.totalPriceNumber) {
      const ticker = normalizeTicker(position.base) || position.base;
      shares[ticker] = (position.totalPriceNumber / totalValue) * 100;
    }
  }
  return shares;
};
```

### Исключения
- **Валюты**: RUB, USD, EUR исключаются из расчета долей
- **Пустые позиции**: Позиции без `totalPriceNumber` игнорируются
- **Нулевые значения**: Корректно обрабатываются

## Файлы

- `src/provider/index.ts` - основная логика
- `src/__tests__/detailed_balancing_output.test.ts` - тесты
- `src/tools/demoDetailedOutput.ts` - демонстрация
- `tasks/detailed_balancing_output.md` - детали задачи

## Совместимость

- ✅ Не влияет на существующую логику
- ✅ Добавлена как дополнительная функциональность
- ✅ Полностью заменяет старый JSON формат

## Будущие улучшения

- [ ] Добавить цветной вывод для лучшей читаемости
- [ ] Экспорт результатов в CSV/Excel
- [ ] Исторический анализ изменений
- [ ] Уведомления о значительных изменениях
- [ ] Веб-интерфейс для мониторинга
